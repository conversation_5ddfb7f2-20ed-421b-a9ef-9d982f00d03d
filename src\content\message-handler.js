/**
 * Message Handler for Content Script
 * Manages chrome extension messaging and communication
 */

import {
  MESSAGE_ACTIONS,
  EXTENSION_CONFIG,
  DEBUG,
} from "../config/constants.js";
import { logDebug, logError, logWarn } from "../utils/logger.utils.js";

/**
 * Handles chrome extension messaging for the content script
 */
export class MessageHandler {
  constructor(stateManager, svgUtils) {
    this.stateManager = stateManager;
    this.svgUtils = svgUtils;
    this.messageListener = null;
    this.isListening = false;
  }

  /**
   * Start listening for messages
   */
  startListening() {
    if (this.isListening) return;

    this.messageListener = (message, sender, sendResponse) => {
      this._handleMessage(message, sender, sendResponse);
      return true; // Keep channel open for async responses
    };

    chrome.runtime.onMessage.addListener(this.messageListener);
    this.isListening = true;

    logDebug("Message handler started listening", "MessageHandler");
  }

  /**
   * Stop listening for messages
   */
  stopListening() {
    if (!this.isListening || !this.messageListener) return;

    chrome.runtime.onMessage.removeListener(this.messageListener);
    this.messageListener = null;
    this.isListening = false;

    logDebug("Message handler stopped listening", "MessageHandler");
  }

  /**
   * Handle incoming messages
   * @param {Object} message - Message object
   * @param {Object} sender - Sender information
   * @param {Function} sendResponse - Response callback
   * @private
   */
  async _handleMessage(message, sender, sendResponse) {
    try {
      logDebug("Received message", "MessageHandler", message);

      const { action } = message;

      switch (action) {
        case MESSAGE_ACTIONS.getSelectionCount:
          await this._handleGetSelectionCount(sendResponse);
          break;

        case MESSAGE_ACTIONS.getSVGData:
          await this._handleGetSVGData(sendResponse);
          break;

        case MESSAGE_ACTIONS.clearSelections:
          await this._handleClearSelections(sendResponse);
          break;

        case MESSAGE_ACTIONS.debugSelections:
          await this._handleDebugSelections(sendResponse);
          break;

        default:
          logWarn(`Unknown action: ${action}`, "MessageHandler");
          sendResponse({
            success: false,
            error: `Unknown action: ${action}`,
          });
          break;
      }
    } catch (error) {
      logError("Message handler error", "MessageHandler", error);
      sendResponse({
        success: false,
        error: error.message,
      });
    }
  }

  /**
   * Handle get selection count request
   * @param {Function} sendResponse - Response callback
   * @private
   */
  async _handleGetSelectionCount(sendResponse) {
    try {
      const count = this.stateManager.getSelectionCount();

      logDebug(`Selection count requested: ${count}`, "MessageHandler");

      sendResponse({
        success: true,
        count: count,
      });
    } catch (error) {
      logError("Error getting selection count", "MessageHandler", error);
      sendResponse({
        success: false,
        error: error.message,
        count: 0,
      });
    }
  }

  /**
   * Handle get SVG data request
   * @param {Function} sendResponse - Response callback
   * @private
   */
  async _handleGetSVGData(sendResponse) {
    try {
      // Check cache first
      const cachedData = this.stateManager.getCachedData();

      if (cachedData) {
        logDebug("Returning cached SVG data", "MessageHandler");
        sendResponse({
          success: true,
          data: cachedData,
        });
        return;
      }

      // Generate new data
      const selectedSVGs = this.stateManager.getSelectedSVGs();

      if (selectedSVGs.size === 0) {
        sendResponse({
          success: false,
          error: "No SVGs selected",
          data: [],
        });
        return;
      }

      logDebug(
        `Processing ${selectedSVGs.size} selected SVGs`,
        "MessageHandler"
      );

      const svgDataArray = await this._processSVGElements(selectedSVGs);

      // Get page context information for filename placeholders
      const pageContext = this._getPageContext();

      // Cache the processed data
      this.stateManager.setCachedData(svgDataArray);

      sendResponse({
        success: true,
        data: svgDataArray,
        pageContext: pageContext,
      });
    } catch (error) {
      logError("Error getting SVG data", "MessageHandler", error);
      sendResponse({
        success: false,
        error: error.message,
        data: [],
      });
    }
  }

  /**
   * Handle clear selections request
   * @param {Function} sendResponse - Response callback
   * @private
   */
  async _handleClearSelections(sendResponse) {
    try {
      const previousCount = this.stateManager.getSelectionCount();
      this.stateManager.clearAllSelections();

      logDebug(`Cleared ${previousCount} selections`, "MessageHandler");

      sendResponse({
        success: true,
        clearedCount: previousCount,
      });
    } catch (error) {
      logError("Error clearing selections", "MessageHandler", error);
      sendResponse({
        success: false,
        error: error.message,
      });
    }
  }

  /**
   * Handle debug selections request
   * @param {Function} sendResponse - Response callback
   * @private
   */
  async _handleDebugSelections(sendResponse) {
    try {
      const selectedSVGs = this.stateManager.getSelectedSVGs();
      const debugInfo = {
        count: selectedSVGs.size,
        elements: [],
      };

      selectedSVGs.forEach((svg, index) => {
        debugInfo.elements.push({
          index: index,
          tagName: svg.tagName,
          id: svg.id || null,
          className: svg.className || null,
          dimensions: {
            width: svg.getBoundingClientRect().width,
            height: svg.getBoundingClientRect().height,
          },
          hasViewBox: svg.hasAttribute("viewBox"),
          viewBox: svg.getAttribute("viewBox") || null,
        });
      });

      logDebug("Debug info", "MessageHandler", debugInfo);

      sendResponse({
        success: true,
        debug: debugInfo,
      });
    } catch (error) {
      logError("Error getting debug info", "MessageHandler", error);
      sendResponse({
        success: false,
        error: error.message,
      });
    }
  }

  /**
   * Process selected SVG elements into data array
   * @param {Set} selectedSVGs - Set of selected SVG elements
   * @returns {Promise<Array>} Array of processed SVG data
   * @private
   */
  async _processSVGElements(selectedSVGs) {
    const svgDataArray = [];
    let index = 0;

    for (const svgElement of selectedSVGs) {
      try {
        const svgData = this.svgUtils.createSVGDataFromElement(svgElement);
        svgData.index = index;
        svgDataArray.push(svgData);
        index++;
      } catch (error) {
        logError(
          `Error processing SVG at index ${index}`,
          "MessageHandler",
          error
        );
        // Continue processing other SVGs even if one fails
      }
    }

    return svgDataArray;
  }

  /**
   * Get page context information for filename placeholders
   * @returns {Object} Page context with domain, title, etc.
   * @private
   */
  _getPageContext() {
    try {
      // Get domain from current page URL
      let domain = "unknown";
      try {
        domain = window.location.hostname || "unknown";
      } catch (e) {
        domain = "unknown";
      }

      // Get page title
      let title = "untitled";
      try {
        title = document.title || "untitled";
        // Clean title for filename use
        title = title
          .trim()
          .replace(/[<>:"/\\|?*]/g, "_")
          .substring(0, 50);
      } catch (e) {
        title = "untitled";
      }

      return {
        domain: domain,
        title: title,
        url: window.location.href || "",
        timestamp: Date.now(),
      };
    } catch (error) {
      logError("Error getting page context", "MessageHandler", error);
      return {
        domain: "unknown",
        title: "untitled",
        url: "",
        timestamp: Date.now(),
      };
    }
  }

  /**
   * Send message to background/popup
   * @param {Object} message - Message to send
   * @returns {Promise<Object>} Response from recipient
   */
  async sendMessage(message) {
    try {
      if (DEBUG.enabled) {
        console.log(`${EXTENSION_CONFIG.logPrefix} Sending message:`, message);
      }

      const response = await chrome.runtime.sendMessage(message);

      if (DEBUG.enabled) {
        console.log(
          `${EXTENSION_CONFIG.logPrefix} Message response:`,
          response
        );
      }

      return response;
    } catch (error) {
      console.error(
        `${EXTENSION_CONFIG.logPrefix} Error sending message:`,
        error
      );
      throw error;
    }
  }

  /**
   * Check if currently listening for messages
   * @returns {boolean} Whether currently listening
   */
  isCurrentlyListening() {
    return this.isListening;
  }

  /**
   * Clean up message handler
   */
  cleanup() {
    this.stopListening();
  }
}
