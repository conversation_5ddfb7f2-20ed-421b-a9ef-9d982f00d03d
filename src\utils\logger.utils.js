/**
 * Logging Utilities
 * Centralized logging functions to reduce repetition
 */

import { EXTENSION_CONFIG, DEBUG } from "../config/constants.js";

/**
 * Log levels enum
 */
export const LOG_LEVELS = {
  DEBUG: "debug",
  INFO: "info",
  WARN: "warn",
  ERROR: "error",
};

/**
 * Create formatted log message with prefix
 * @param {string} message - Log message
 * @param {string} context - Optional context
 * @returns {string} Formatted message
 */
function formatMessage(message, context = "") {
  const contextStr = context ? ` [${context}]` : "";
  return `${EXTENSION_CONFIG.logPrefix}${contextStr} ${message}`;
}

/**
 * Log debug message (only in debug mode)
 * @param {string} message - Message to log
 * @param {string} context - Optional context
 * @param {*} data - Optional additional data
 */
export function logDebug(message, context = "", data = null) {
  if (DEBUG.enabled) {
    const formatted = formatMessage(message, context);
    if (data) {
      console.log(formatted, data);
    } else {
      console.log(formatted);
    }
  }
}

/**
 * Log info message
 * @param {string} message - Message to log
 * @param {string} context - Optional context
 * @param {*} data - Optional additional data
 */
export function logInfo(message, context = "", data = null) {
  const formatted = formatMessage(message, context);
  if (data) {
    console.log(formatted, data);
  } else {
    console.log(formatted);
  }
}

/**
 * Log warning message
 * @param {string} message - Message to log
 * @param {string} context - Optional context
 * @param {*} data - Optional additional data
 */
export function logWarn(message, context = "", data = null) {
  const formatted = formatMessage(message, context);
  if (data) {
    console.warn(formatted, data);
  } else {
    console.warn(formatted);
  }
}

/**
 * Log error message
 * @param {string} message - Message to log
 * @param {string} context - Optional context
 * @param {*} data - Optional additional data
 */
export function logError(message, context = "", data = null) {
  const formatted = formatMessage(message, context);
  if (data) {
    console.error(formatted, data);
  } else {
    console.error(formatted);
  }
}
