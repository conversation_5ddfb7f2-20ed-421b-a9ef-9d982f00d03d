/**
 * Logging Utilities
 * Centralized logging functions to reduce repetition
 */

import { EXTENSION_CONFIG, DEBUG } from "../config/constants.js";

/**
 * Log levels enum
 */
export const LOG_LEVELS = {
  DEBUG: "debug",
  INFO: "info",
  WARN: "warn",
  ERROR: "error",
};

/**
 * Create formatted log message with prefix
 * @param {string} message - Log message
 * @param {string} context - Optional context
 * @returns {string} Formatted message
 */
function formatMessage(message, context = "") {
  const contextStr = context ? ` [${context}]` : "";
  return `${EXTENSION_CONFIG.logPrefix}${contextStr} ${message}`;
}

/**
 * Log debug message (only in debug mode)
 * @param {string} message - Message to log
 * @param {string} context - Optional context
 * @param {*} data - Optional additional data
 */
export function logDebug(message, context = "", data = null) {
  if (DEBUG.enabled) {
    const formatted = formatMessage(message, context);
    if (data) {
      console.log(formatted, data);
    } else {
      console.log(formatted);
    }
  }
}

/**
 * Log info message
 * @param {string} message - Message to log
 * @param {string} context - Optional context
 * @param {*} data - Optional additional data
 */
export function logInfo(message, context = "", data = null) {
  const formatted = formatMessage(message, context);
  if (data) {
    console.log(formatted, data);
  } else {
    console.log(formatted);
  }
}

/**
 * Log warning message
 * @param {string} message - Message to log
 * @param {string} context - Optional context
 * @param {*} data - Optional additional data
 */
export function logWarn(message, context = "", data = null) {
  const formatted = formatMessage(message, context);
  if (data) {
    console.warn(formatted, data);
  } else {
    console.warn(formatted);
  }
}

/**
 * Log error message
 * @param {string} message - Message to log
 * @param {string} context - Optional context
 * @param {*} data - Optional additional data
 */
export function logError(message, context = "", data = null) {
  const formatted = formatMessage(message, context);
  if (data) {
    console.error(formatted, data);
  } else {
    console.error(formatted);
  }
}

/**
 * Log performance metrics
 * @param {string} operation - Operation name
 * @param {number} duration - Duration in milliseconds
 * @param {string} context - Optional context
 * @param {Object} additionalData - Additional performance data
 */
export function logPerformance(
  operation,
  duration,
  context = "",
  additionalData = null
) {
  if (DEBUG.enabled) {
    const formatted = formatMessage(
      `Performance [${operation}]: ${duration.toFixed(2)}ms`,
      context
    );
    if (additionalData) {
      console.log(formatted, additionalData);
    } else {
      console.log(formatted);
    }
  }
}

/**
 * Conditional logging - only logs if condition is true
 * @param {boolean} condition - Condition to check
 * @param {string} level - Log level ('debug', 'info', 'warn', 'error')
 * @param {string} message - Message to log
 * @param {string} context - Optional context
 * @param {*} data - Optional additional data
 */
export function logConditional(
  condition,
  level,
  message,
  context = "",
  data = null
) {
  if (!condition) return;

  switch (level) {
    case LOG_LEVELS.DEBUG:
      logDebug(message, context, data);
      break;
    case LOG_LEVELS.INFO:
      logInfo(message, context, data);
      break;
    case LOG_LEVELS.WARN:
      logWarn(message, context, data);
      break;
    case LOG_LEVELS.ERROR:
      logError(message, context, data);
      break;
    default:
      logInfo(message, context, data);
  }
}

/**
 * Safe logging function that handles potential errors in logging itself
 * @param {Function} logFn - Logging function to call
 * @param {string} fallbackMessage - Fallback message if logging fails
 */
export function safeLog(logFn, fallbackMessage = "Logging error occurred") {
  try {
    logFn();
  } catch (error) {
    console.error(`${EXTENSION_CONFIG.logPrefix} ${fallbackMessage}:`, error);
  }
}
