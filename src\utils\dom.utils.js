/**
 * DOM Utility Functions
 * Centralizes common DOM manipulation tasks
 */

import { ELEMENT_IDS } from "../config/constants.js";

/**
 * Get element by ID with optional error handling
 * @param {string} id - Element ID
 * @param {boolean} required - Whether element is required (throws error if not found)
 * @returns {HTMLElement|null} Element or null if not found
 */
export function getElementById(id, required = false) {
  const element = document.getElementById(id);

  if (required && !element) {
    throw new Error(`Required element with ID '${id}' not found`);
  }

  return element;
}

/**
 * Get multiple elements by their IDs
 * @param {string[]} ids - Array of element IDs
 * @returns {Object} Object with ID as key and element as value
 */
export function getElementsByIds(ids) {
  const elements = {};
  ids.forEach((id) => {
    elements[id] = document.getElementById(id);
  });
  return elements;
}

/**
 * Unified visibility control for elements
 * @param {HTMLElement|string} element - Element or element ID
 * @param {boolean} visible - Whether to show (true) or hide (false)
 * @param {string} displayType - Display type to use when showing ('block', 'inline', 'flex', etc.)
 */
export function setVisible(element, visible, displayType = "block") {
  const el = typeof element === "string" ? getElementById(element) : element;
  if (!el) return;

  if (visible) {
    el.classList.remove("hidden");
    el.style.display = displayType;
  } else {
    el.classList.add("hidden");
    el.style.display = "none";
  }
}

/**
 * Show element - convenience wrapper
 * @param {HTMLElement|string} element - Element or element ID
 * @param {string} displayType - Display type to use ('block', 'inline', 'flex', etc.)
 */
export function show(element, displayType = "block") {
  setVisible(element, true, displayType);
}

/**
 * Hide element - convenience wrapper
 * @param {HTMLElement|string} element - Element or element ID
 */
export function hide(element) {
  setVisible(element, false);
}

/**
 * Toggle element visibility
 * @param {HTMLElement|string} element - Element or element ID
 * @param {boolean} visible - Optional explicit visibility state
 * @param {string} displayType - Display type to use when showing ('block', 'inline', 'flex', etc.)
 */
export function toggle(element, visible, displayType = "block") {
  const el = typeof element === "string" ? getElementById(element) : element;
  if (!el) return;

  const isCurrentlyVisible =
    el.style.display !== "none" && !el.classList.contains("hidden");
  const shouldShow = visible !== undefined ? visible : !isCurrentlyVisible;
  setVisible(el, shouldShow, displayType);
}

/**
 * Set element text content safely
 * @param {HTMLElement|string} element - Element or element ID
 * @param {string} text - Text content
 */
export function setText(element, text) {
  const el = typeof element === "string" ? getElementById(element) : element;
  if (el) {
    el.textContent = text;
  }
}

/**
 * Set element HTML content safely
 * @param {HTMLElement|string} element - Element or element ID
 * @param {string} html - HTML content
 */
export function setHTML(element, html) {
  const el = typeof element === "string" ? getElementById(element) : element;
  if (el) {
    el.innerHTML = html;
  }
}

/**
 * Get element value (for inputs, selects, etc.)
 * @param {HTMLElement|string} element - Element or element ID
 * @returns {string} Element value
 */
export function getValue(element) {
  const el = typeof element === "string" ? getElementById(element) : element;
  return el ? el.value : "";
}

/**
 * Set element value
 * @param {HTMLElement|string} element - Element or element ID
 * @param {string} value - Value to set
 */
export function setValue(element, value) {
  const el = typeof element === "string" ? getElementById(element) : element;
  if (el) {
    el.value = value;
  }
}

/**
 * Get checkbox checked state
 * @param {HTMLElement|string} element - Element or element ID
 * @returns {boolean} Checked state
 */
export function isChecked(element) {
  const el = typeof element === "string" ? getElementById(element) : element;
  return el ? el.checked : false;
}

/**
 * Set checkbox checked state
 * @param {HTMLElement|string} element - Element or element ID
 * @param {boolean} checked - Checked state
 */
export function setChecked(element, checked) {
  const el = typeof element === "string" ? getElementById(element) : element;
  if (el) {
    el.checked = checked;
  }
}

/**
 * Enable/disable element
 * @param {HTMLElement|string} element - Element or element ID
 * @param {boolean} enabled - Whether element should be enabled
 */
export function setEnabled(element, enabled) {
  const el = typeof element === "string" ? getElementById(element) : element;
  if (el) {
    el.disabled = !enabled;
  }
}

/**
 * CSS class manipulation utilities
 */
export function addClass(element, className) {
  const el = typeof element === "string" ? getElementById(element) : element;
  if (el) el.classList.add(className);
}

export function removeClass(element, className) {
  const el = typeof element === "string" ? getElementById(element) : element;
  if (el) el.classList.remove(className);
}

export function toggleClass(element, className, force) {
  const el = typeof element === "string" ? getElementById(element) : element;
  if (el) {
    if (force !== undefined) {
      el.classList.toggle(className, force);
    } else {
      el.classList.toggle(className);
    }
  }
}

export function hasClass(element, className) {
  const el = typeof element === "string" ? getElementById(element) : element;
  return el ? el.classList.contains(className) : false;
}

/**
 * Create element with attributes and content
 * @param {string} tagName - HTML tag name
 * @param {Object} attributes - Element attributes
 * @param {string} content - Text content
 * @returns {HTMLElement} Created element
 */
export function createElement(tagName, attributes = {}, content = "") {
  const element = document.createElement(tagName);

  Object.entries(attributes).forEach(([key, value]) => {
    if (key === "className") {
      element.className = value;
    } else if (key === "textContent") {
      element.textContent = value;
    } else if (key === "innerHTML") {
      element.innerHTML = value;
    } else {
      element.setAttribute(key, value);
    }
  });

  if (content) {
    element.textContent = content;
  }

  return element;
}

/**
 * Remove element from DOM
 * @param {HTMLElement|string} element - Element or element ID
 */
export function removeElement(element) {
  const el = typeof element === "string" ? getElementById(element) : element;
  if (el && el.parentNode) {
    el.parentNode.removeChild(el);
  }
}

/**
 * Get selected radio button value from a group
 * @param {string} name - Radio button group name
 * @returns {string|null} Selected value or null
 */
export function getSelectedRadioValue(name) {
  const selected = document.querySelector(`input[name="${name}"]:checked`);
  return selected ? selected.value : null;
}

/**
 * Set progress bar value
 * @param {number} percentage - Progress percentage (0-100)
 */
export function setProgress(percentage) {
  const progressBar = getElementById(ELEMENT_IDS.progressBar);
  const progressFill = getElementById(ELEMENT_IDS.progressBarFill);

  if (progressBar && progressFill) {
    if (percentage > 0) {
      show(progressBar);
      progressFill.style.width = `${Math.min(100, Math.max(0, percentage))}%`;
    } else {
      hide(progressBar);
      progressFill.style.width = "0%";
    }
  }
}

/**
 * Clear all child elements from a container
 * @param {HTMLElement|string} container - Container element or ID
 */
export function clearChildren(container) {
  const el =
    typeof container === "string" ? getElementById(container) : container;
  if (el) {
    while (el.firstChild) {
      el.removeChild(el.firstChild);
    }
  }
}
