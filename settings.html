<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>SVG Switcher Settings</title>
    <link rel="stylesheet" href="styles/variables.css" />
    <link rel="stylesheet" href="styles/settings.css" />
    <link rel="stylesheet" href="styles/about.css" />
  </head>
  <body>
    <div class="settings-container">
      <!-- Floating Background Elements -->
      <div class="floating-orb"></div>
      <div class="floating-orb"></div>

      <header class="settings-header" id="dynamicHeader">
        <!-- Geometric Pattern Overlay -->
        <div class="geometric-pattern">
          <div class="pattern-grid" data-pattern-count="7"></div>
        </div>

        <!-- Advanced Background Effects -->
        <div class="header-background">
          <!-- Dynamic Gradient Mesh -->
          <div class="gradient-mesh">
            <div class="mesh-blob blob-1"></div>
            <div class="mesh-blob blob-2"></div>
            <div class="mesh-blob blob-3"></div>
            <div class="mesh-blob blob-4"></div>
          </div>

          <!-- Enhanced Gradient Bar -->
          <div class="header-gradient">
            <div class="gradient-shine"></div>
          </div>

          <!-- Interactive Particle System -->
          <div class="particle-system">
            <div
              class="particle-layer primary-particles"
              data-particle-count="5"
              data-particle-type="interactive"
            ></div>
            <div
              class="particle-layer secondary-particles"
              data-particle-count="8"
              data-particle-type="micro"
            ></div>
          </div>

          <!-- Starfield Background -->
          <div class="starfield" data-star-count="10"></div>
        </div>

        <!-- Interactive Light Rays -->
        <div class="light-rays">
          <div class="light-ray ray-1"></div>
          <div class="light-ray ray-2"></div>
          <div class="light-ray ray-3"></div>
        </div>

        <div class="header-content">
          <div class="title-section">
            <!-- Enhanced Title Icon with Multiple Layers -->
            <div class="title-icon-container">
              <div class="icon-backdrop"></div>
              <div class="title-icon">
                <div class="icon-glow"></div>
                <img
                  src="icons/icon-32.png"
                  width="32"
                  height="32"
                  alt="SVG2PNG Logo"
                  class="main-icon"
                />
                <div class="icon-sparkles" data-sparkle-count="4"></div>
              </div>
              <div class="icon-orbital-ring"></div>
            </div>

            <div class="title-text">
              <h1 class="animated-title">
                <span class="title-word" data-word="SVG">SVG</span>
                <span class="title-word" data-word="Switcher">Switcher</span>
                <span class="title-word" data-word="Settings">Settings</span>
              </h1>
              <p class="header-subtitle">
                <span class="subtitle-highlight">Customize</span> your SVG
                conversion experience
                <span class="subtitle-cursor">|</span>
              </p>
            </div>
          </div>

          <div class="header-actions">
            <div class="action-group">
              <button
                id="resetAllBtn"
                class="btn btn-secondary tooltip enhanced-btn"
                data-tooltip="Reset all settings to their default values"
              >
                <div class="btn-background">
                  <div class="btn-gradient"></div>
                  <div class="btn-shine"></div>
                </div>
                <div class="btn-content">
                  <div class="btn-icon">
                    <svg
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                    >
                      <path
                        d="M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8"
                      ></path>
                      <path d="M3 3v5h5"></path>
                    </svg>
                  </div>
                  <span class="btn-text">Reset to Defaults</span>
                </div>
                <div class="btn-ripple"></div>
                <div class="btn-particles">
                  <span class="btn-particle"></span>
                  <span class="btn-particle"></span>
                  <span class="btn-particle"></span>
                </div>
              </button>

              <button
                id="saveSettingsBtn"
                class="btn btn-primary tooltip enhanced-btn"
                data-tooltip="Save all current settings permanently"
              >
                <div class="btn-background">
                  <div class="btn-gradient"></div>
                  <div class="btn-shine"></div>
                </div>
                <div class="btn-content">
                  <div class="btn-icon">
                    <svg
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                    >
                      <path
                        d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"
                      ></path>
                      <polyline points="17 21 17 13 7 13 7 21"></polyline>
                      <polyline points="7 3 7 8 15 8"></polyline>
                    </svg>
                  </div>
                  <span class="btn-text">Save All Settings</span>
                </div>
                <div class="btn-ripple"></div>
                <div class="btn-particles">
                  <span class="btn-particle"></span>
                  <span class="btn-particle"></span>
                  <span class="btn-particle"></span>
                </div>
              </button>
            </div>
          </div>
        </div>

        <!-- Enhanced Wave with Morphing Effects -->
        <div class="header-wave enhanced-wave">
          <div class="wave-layer wave-primary">
            <svg viewBox="0 0 1200 120" preserveAspectRatio="none">
              <path
                d="M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z"
                class="morphing-path"
              ></path>
            </svg>
          </div>
          <div class="wave-layer wave-secondary">
            <svg viewBox="0 0 1200 120" preserveAspectRatio="none">
              <path
                d="M0,0V15.81C13,36.92,27.64,56.86,47.69,72.05,99.41,111.27,165,111,224.58,91.58c31.15-10.15,60.09-26.07,89.67-39.8,40.92-19,84.73-46,130.83-49.67,36.26-2.85,70.9,9.42,98.6,31.56,31.77,25.39,62.32,62,103.63,73,40.44,10.79,81.35-6.69,119.13-24.28s75.16-39,116.92-43.05c59.73-5.85,113.28,22.88,168.9,38.84,30.2,8.66,59,6.17,87.09-7.5,22.43-10.89,48-26.93,60.65-49.24V0Z"
                class="morphing-path"
              ></path>
            </svg>
          </div>
          <div class="wave-shimmer"></div>
        </div>
      </header>

      <nav
        class="settings-nav"
        role="navigation"
        aria-label="Settings navigation"
      >
        <ul class="nav-list">
          <li>
            <a href="#general" class="nav-link active" data-section="general">
              <svg
                width="18"
                height="18"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
              >
                <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
                <polyline points="9 22 9 12 15 12 15 22"></polyline>
              </svg>
              General
            </a>
          </li>
          <li>
            <a href="#export" class="nav-link" data-section="export">
              <svg
                width="18"
                height="18"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
              >
                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                <polyline points="7 10 12 15 17 10"></polyline>
                <line x1="12" y1="15" x2="12" y2="3"></line>
              </svg>
              Export Options
            </a>
          </li>
          <li>
            <a href="#behavior" class="nav-link" data-section="behavior">
              <svg
                width="18"
                height="18"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
              >
                <path
                  d="M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z"
                ></path>
              </svg>
              Behavior
            </a>
          </li>
          <li>
            <a href="#presets" class="nav-link" data-section="presets">
              <svg
                width="18"
                height="18"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
              >
                <path
                  d="M19 21l-7-5-7 5V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2z"
                ></path>
              </svg>
              Presets
            </a>
          </li>
          <li>
            <a href="#advanced" class="nav-link" data-section="advanced">
              <svg
                width="18"
                height="18"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
              >
                <circle cx="12" cy="12" r="3"></circle>
                <path
                  d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"
                ></path>
              </svg>
              Advanced
            </a>
          </li>
          <li>
            <a href="#backup" class="nav-link" data-section="backup">
              <svg
                width="18"
                height="18"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
              >
                <path d="M12 2v4"></path>
                <path d="M12 18v4"></path>
                <path d="m4.93 4.93 2.83 2.83"></path>
                <path d="m16.24 16.24 2.83 2.83"></path>
                <path d="M2 12h4"></path>
                <path d="M18 12h4"></path>
                <path d="m4.93 19.07 2.83-2.83"></path>
                <path d="m16.24 7.76 2.83-2.83"></path>
              </svg>
              Backup & Restore
            </a>
          </li>
          <li>
            <a href="#about" class="nav-link" data-section="about">
              <svg
                width="18"
                height="18"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              >
                <circle cx="12" cy="12" r="10"></circle>
                <path d="M12 16v-4"></path>
                <circle cx="12" cy="8" r="1"></circle>
              </svg>
              About
            </a>
          </li>
        </ul>
      </nav>

      <main class="settings-content">
        <!-- General Settings Section -->
        <section
          id="general"
          class="settings-section active"
          data-section="general"
        >
          <h2>
            <svg
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
            >
              <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
              <polyline points="9 22 9 12 15 12 15 22"></polyline>
            </svg>
            General Settings
          </h2>

          <div class="setting-group">
            <h3>
              <svg
                width="18"
                height="18"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
              >
                <path
                  d="M17.5 19H9a7 7 0 1 1 6.71-9h1.79a4.5 4.5 0 1 1 0 9Z"
                ></path>
              </svg>
              Default Filename Settings
            </h3>

            <div class="setting-item">
              <label for="defaultFilename">Default Filename Pattern:</label>
              <input
                type="text"
                id="defaultFilename"
                class="form-control"
                value="image"
                placeholder="Enter filename pattern..."
              />
              <small class="setting-description">
                Use placeholders: {date}, {time}, {index}, {title}, {domain}
              </small>
            </div>

            <div class="setting-item">
              <label for="filenameCase">Filename Case:</label>
              <select id="filenameCase" class="form-control">
                <option value="original">Keep Original</option>
                <option value="lowercase">Lowercase</option>
                <option value="uppercase">Uppercase</option>
                <option value="camelcase">camelCase</option>
                <option value="kebabcase">kebab-case</option>
              </select>
            </div>

            <div class="setting-item checkbox-item">
              <label class="checkbox-label">
                <input type="checkbox" id="replaceSpaces" />
                <span class="checkmark"></span>
                Replace spaces with underscores
              </label>
            </div>

            <div class="setting-item checkbox-item">
              <label class="checkbox-label">
                <input type="checkbox" id="appendTimestamp" />
                <span class="checkmark"></span>
                Append timestamp to prevent conflicts
              </label>
            </div>
          </div>

          <div class="setting-group">
            <h3>
              <svg
                width="18"
                height="18"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
              >
                <path d="M6 18L18 6M6 6l12 12"></path>
              </svg>
              Default Quality & Scaling
            </h3>

            <div class="setting-item">
              <label for="defaultQuality">Default Quality Scale:</label>
              <select id="defaultQuality" class="form-control">
                <option value="1">Original Size (1x)</option>
                <option value="2">High Quality (2x)</option>
                <option value="3">Ultra Quality (3x)</option>
                <option value="4">Maximum Quality (4x)</option>
              </select>
            </div>

            <div class="setting-item">
              <label for="maxDimension">Maximum Dimension Limit (px):</label>
              <input
                type="number"
                id="maxDimension"
                class="form-control"
                min="100"
                max="16384"
                value="8192"
              />
              <small class="setting-description">
                Prevent memory issues with very large images
              </small>
            </div>
          </div>
        </section>

        <!-- Export Options Section -->
        <section id="export" class="settings-section" data-section="export">
          <h2>Export Options</h2>

          <div class="setting-group">
            <h3>Default Format Settings</h3>

            <div class="setting-item">
              <label for="defaultFormat">Default Export Format:</label>
              <select id="defaultFormat" class="form-control">
                <option value="png">PNG</option>
                <option value="jpeg">JPEG</option>
                <option value="webp">WebP</option>
              </select>
            </div>
          </div>

          <div class="setting-group">
            <h3>Background Settings</h3>

            <div class="setting-item checkbox-item">
              <label class="checkbox-label">
                <input type="checkbox" id="defaultTransparent" checked />
                <span class="checkmark"></span>
                Use transparent background by default
              </label>
            </div>

            <div class="setting-item">
              <label for="defaultBackgroundColor"
                >Default Background Color:</label
              >
              <input
                type="color"
                id="defaultBackgroundColor"
                value="#ffffff"
                class="color-control"
              />
            </div>

            <div class="setting-item checkbox-item">
              <label class="checkbox-label">
                <input type="checkbox" id="preserveAspectRatio" checked />
                <span class="checkmark"></span>
                Preserve aspect ratio when resizing
              </label>
            </div>
          </div>
        </section>

        <!-- Behavior Settings Section -->
        <section id="behavior" class="settings-section" data-section="behavior">
          <h2>Behavior Settings</h2>

          <div class="setting-group">
            <h3>User Interface</h3>

            <div class="setting-item checkbox-item">
              <label class="checkbox-label">
                <input type="checkbox" id="showToolbarOnSelection" checked />
                <span class="checkmark"></span>
                Show floating toolbar when SVGs are selected
              </label>
            </div>

            <div class="setting-item checkbox-item">
              <label class="checkbox-label">
                <input type="checkbox" id="autoHideToolbar" />
                <span class="checkmark"></span>
                Auto-hide toolbar after 10 seconds of inactivity
              </label>
            </div>

            <div class="setting-item">
              <label for="toolbarPosition">Default Toolbar Position:</label>
              <select id="toolbarPosition" class="form-control">
                <option value="top-right">Top Right</option>
                <option value="top-left">Top Left</option>
                <option value="bottom-right">Bottom Right</option>
                <option value="bottom-left">Bottom Left</option>
              </select>
            </div>

            <div class="setting-item checkbox-item">
              <label class="checkbox-label">
                <input type="checkbox" id="rememberToolbarPosition" />
                <span class="checkmark"></span>
                Remember toolbar position when dragged
              </label>
            </div>
          </div>

          <div class="setting-group">
            <h3>Selection Behavior</h3>

            <div class="setting-item">
              <label for="selectionModifier">Selection Modifier Key:</label>
              <select id="selectionModifier" class="form-control">
                <option value="ctrl">Ctrl (Cmd on Mac)</option>
                <option value="shift">Shift</option>
                <option value="alt">Alt</option>
                <option value="none">None (Click to select)</option>
              </select>
            </div>

            <div class="setting-item checkbox-item">
              <label class="checkbox-label">
                <input type="checkbox" id="clearOnEscape" checked />
                <span class="checkmark"></span>
                Clear selections when Escape is pressed
              </label>
            </div>

            <div class="setting-item checkbox-item">
              <label class="checkbox-label">
                <input type="checkbox" id="visualFeedback" checked />
                <span class="checkmark"></span>
                Show visual feedback on SVG selection
              </label>
            </div>
          </div>

          <div class="setting-group">
            <h3>Download Behavior</h3>

            <div class="setting-item">
              <label for="defaultDownloadType"
                >Default Download Type for Multiple SVGs:</label
              >
              <select id="defaultDownloadType" class="form-control">
                <option value="separate">Separate Files</option>
                <option value="zip">ZIP Archive</option>
              </select>
            </div>

            <div class="setting-item checkbox-item">
              <label class="checkbox-label">
                <input type="checkbox" id="showPreview" checked />
                <span class="checkmark"></span>
                Show preview for single SVG conversions
              </label>
            </div>

            <div class="setting-item checkbox-item">
              <label class="checkbox-label">
                <input type="checkbox" id="confirmBeforeDownload" />
                <span class="checkmark"></span>
                Confirm before downloading multiple files
              </label>
            </div>
          </div>
        </section>

        <!-- Presets Section -->
        <section id="presets" class="settings-section" data-section="presets">
          <h2>Preset Management</h2>

          <div class="setting-group">
            <h3>Saved Presets</h3>
            <div id="presetsList" class="presets-list">
              <!-- Presets will be populated here -->
            </div>
          </div>

          <div class="setting-group">
            <h3>Create New Preset</h3>
            <div class="preset-form">
              <div class="form-row">
                <input
                  type="text"
                  id="newPresetName"
                  placeholder="Preset name..."
                  class="form-control"
                />
                <button id="createPresetBtn" class="btn btn-primary">
                  Create Preset
                </button>
              </div>

              <div class="preset-settings">
                <div class="form-row">
                  <label for="presetQuality">Quality:</label>
                  <select id="presetQuality" class="form-control">
                    <option value="1">1x</option>
                    <option value="2">2x</option>
                    <option value="3">3x</option>
                    <option value="4">4x</option>
                  </select>
                </div>

                <div class="form-row">
                  <label for="presetFormat">Format:</label>
                  <select id="presetFormat" class="form-control">
                    <option value="png">PNG</option>
                    <option value="jpeg">JPEG</option>
                    <option value="webp">WebP</option>
                  </select>
                </div>

                <div class="form-row">
                  <label for="presetBackground">Background:</label>
                  <input
                    type="color"
                    id="presetBackground"
                    value="#ffffff"
                    class="color-control"
                  />
                  <label class="checkbox-label">
                    <input type="checkbox" id="presetTransparent" />
                    <span class="checkmark"></span>
                    Transparent
                  </label>
                </div>
              </div>
            </div>
          </div>
        </section>

        <!-- Advanced Settings Section -->
        <section id="advanced" class="settings-section" data-section="advanced">
          <h2>Advanced Settings</h2>

          <div class="setting-group">
            <h3>Performance</h3>

            <div class="setting-item">
              <label for="maxConcurrentConversions"
                >Max Concurrent Conversions:</label
              >
              <input
                type="number"
                id="maxConcurrentConversions"
                class="form-control"
                min="1"
                max="10"
                value="3"
              />
              <small class="setting-description">
                Higher values may cause browser slowdown
              </small>
            </div>

            <div class="setting-item">
              <label for="conversionTimeout"
                >Conversion Timeout (seconds):</label
              >
              <input
                type="number"
                id="conversionTimeout"
                class="form-control"
                min="5"
                max="120"
                value="30"
              />
            </div>

            <div class="setting-item checkbox-item">
              <label class="checkbox-label">
                <input type="checkbox" id="enableCaching" checked />
                <span class="checkmark"></span>
                Enable conversion result caching
              </label>
            </div>

            <div class="setting-item">
              <label for="cacheSize">Cache Size (items):</label>
              <input
                type="number"
                id="cacheSize"
                class="form-control"
                min="10"
                max="200"
                value="50"
              />
            </div>
          </div>

          <div class="setting-group">
            <h3>Debugging</h3>

            <div class="setting-item checkbox-item">
              <label class="checkbox-label">
                <input type="checkbox" id="enableDebugMode" />
                <span class="checkmark"></span>
                Enable debug logging
              </label>
            </div>

            <div class="setting-item checkbox-item">
              <label class="checkbox-label">
                <input type="checkbox" id="showPerformanceMetrics" />
                <span class="checkmark"></span>
                Show performance metrics in console
              </label>
            </div>
          </div>

          <div class="setting-group">
            <h3>Accessibility</h3>

            <div class="setting-item checkbox-item">
              <label class="checkbox-label">
                <input type="checkbox" id="enableScreenReaderSupport" checked />
                <span class="checkmark"></span>
                Enable screen reader announcements
              </label>
            </div>

            <div class="setting-item checkbox-item">
              <label class="checkbox-label">
                <input type="checkbox" id="highContrastMode" />
                <span class="checkmark"></span>
                High contrast selection indicators
              </label>
            </div>

            <div class="setting-item checkbox-item">
              <label class="checkbox-label">
                <input type="checkbox" id="reduceMotion" />
                <span class="checkmark"></span>
                Reduce animations and transitions
              </label>
            </div>
          </div>
        </section>

        <!-- Backup & Restore Section -->
        <section id="backup" class="settings-section" data-section="backup">
          <h2>Backup & Restore</h2>

          <div class="setting-group">
            <h3>Export Settings</h3>
            <p class="setting-description">
              Export all your settings and presets to a file for backup or
              sharing.
            </p>
            <button id="exportSettingsBtn" class="btn btn-primary">
              Export All Settings
            </button>
          </div>

          <div class="setting-group">
            <h3>Import Settings</h3>
            <p class="setting-description">
              Import settings from a previously exported file. This will
              overwrite current settings.
            </p>
            <input
              type="file"
              id="importSettingsFile"
              accept=".json"
              class="file-input"
            />
            <button id="importSettingsBtn" class="btn btn-secondary">
              Import Settings
            </button>
          </div>

          <div class="setting-group">
            <h3>Reset</h3>
            <p class="setting-description">
              Reset all settings to default values. This action cannot be
              undone.
            </p>
            <button id="resetToDefaultsBtn" class="btn btn-danger">
              Reset All to Defaults
            </button>
          </div>
        </section>

        <!-- About the Developer Section -->
        <section id="about" class="settings-section" data-section="about">
          <div class="about-header">
            <h2>About SVG Switcher</h2>
            <div class="version-badge">
              Version <span id="extensionVersion">2.0.0</span>
            </div>
          </div>

          <!-- Enhanced Extension Overview Card -->
          <div
            class="about-card feature-highlight-card-modern interactive"
            role="region"
            aria-labelledby="extension-overview"
          >
            <!-- Subtle Background Pattern -->
            <div class="card-background-pattern" aria-hidden="true"></div>

            <div class="card-content-modern">
              <!-- Modern Hero Section -->
              <div class="feature-hero">
                <div class="hero-icon">
                  <div class="icon-glow-ring"></div>
                  <svg
                    width="32"
                    height="32"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                  >
                    <path
                      d="M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z"
                    />
                    <circle cx="12" cy="13" r="3" />
                  </svg>
                </div>
                <div class="hero-content">
                  <h3 id="extension-overview" class="hero-title">
                    SVG Switcher Extension
                  </h3>
                  <div class="hero-badges">
                    <span class="badge badge-primary">Professional</span>
                    <span class="badge badge-success">Free</span>
                  </div>
                </div>
              </div>

              <!-- Enhanced Description -->
              <p class="feature-description-modern">
                Transform SVG images to PNG, JPEG, and WebP formats instantly
                while browsing. Professional-grade quality with advanced
                customization options.
              </p>

              <!-- Modern Feature Grid -->
              <div class="feature-grid-modern">
                <div class="feature-item">
                  <div class="feature-icon">
                    <svg
                      width="20"
                      height="20"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                    >
                      <path
                        d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"
                      />
                      <polyline points="14 2 14 8 20 8" />
                      <line x1="16" y1="13" x2="8" y2="13" />
                      <line x1="16" y1="17" x2="8" y2="17" />
                      <polyline points="10 9 9 9 8 9" />
                    </svg>
                  </div>
                  <div class="feature-content">
                    <span class="feature-number" data-count="4">4</span>
                    <span class="feature-label">Formats</span>
                  </div>
                </div>

                <div class="feature-item">
                  <div class="feature-icon">
                    <svg
                      width="20"
                      height="20"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                    >
                      <path d="M20 6 9 17l-5-5" />
                    </svg>
                  </div>
                  <div class="feature-content">
                    <span class="feature-number">100%</span>
                    <span class="feature-label">Free</span>
                  </div>
                </div>

                <div class="feature-item">
                  <div class="feature-icon">
                    <svg
                      width="20"
                      height="20"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                    >
                      <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2" />
                      <circle cx="9" cy="7" r="4" />
                      <path d="M23 21v-2a4 4 0 0 0-3-3.87" />
                      <path d="M16 3.13a4 4 0 0 1 0 7.75" />
                    </svg>
                  </div>
                  <div class="feature-content">
                    <span class="feature-number" data-count="10+">10+</span>
                    <span class="feature-label">Users</span>
                  </div>
                </div>

                <div class="feature-item">
                  <div class="feature-icon">
                    <svg
                      width="20"
                      height="20"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                    >
                      <polygon
                        points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"
                      />
                    </svg>
                  </div>
                  <div class="feature-content">
                    <span class="feature-number">5.0</span>
                    <span class="feature-label">Rating</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Developer Information Card with Soft Shadow -->
          <div class="about-card dev-profile-card">
            <div class="card-content">
              <div class="developer-showcase">
                <!-- Enhanced Developer Header -->
                <div class="developer-header">
                  <div class="developer-avatar-enhanced">
                    <div
                      class="avatar-status online"
                      title="Available for collaboration"
                    ></div>
                    <div class="avatar-ring-enhanced"></div>
                    <div class="avatar-glow"></div>
                    <img
                      src="icons/developer.jpg"
                      alt="Reda Elsayed - Full Stack Developer"
                      class="developer-photo-enhanced"
                      width="120"
                      height="120"
                    />
                    <div class="avatar-badge">
                      <span class="badge-icon">⚡</span>
                    </div>
                  </div>
                  <div class="developer-intro">
                    <div class="intro-header">
                      <h3 class="section-title">👋 Meet the Developer</h3>
                    </div>

                    <div class="name-section">
                      <h4 class="developer-name">Reda Elsayed</h4>
                      <div class="name-decoration"></div>
                    </div>
                  </div>
                </div>

                <!-- Professional Quote -->
                <div class="developer-quote">
                  <div class="quote-mark">"</div>
                  <p class="quote-text">
                    Turning ‘Ctrl + C’ and ‘Ctrl + V’ into art.
                  </p>
                  <div class="quote-author">— Reda || Copypaste Artist</div>
                </div>

                <!-- Enhanced Bio Section -->
                <div class="developer-bio-enhanced">
                  <p class="bio-text">
                    I'm a passionate Full Stack .NET Developer with expertise in
                    building practical tools, browser extensions, and web
                    applications that enhance productivity and improve user
                    experiences. I specialize in creating elegant, efficient
                    solutions that solve real-world problems and make everyday
                    tasks simpler and smarter.
                  </p>
                </div>

                <!-- Enhanced Social Links -->
                <div class="developer-social">
                  <h5 class="social-title">🔗 Connect & Collaborate</h5>
                  <div class="social-links-enhanced">
                    <a
                      href="https://www.linkedin.com/in/redaelsayed/"
                      class="social-link linkedin"
                      target="_blank"
                      rel="noopener noreferrer"
                      aria-label="LinkedIn Profile - Professional network"
                      data-platform="LinkedIn"
                    >
                      <div class="link-background"></div>
                      <div class="link-icon">
                        <svg
                          width="20"
                          height="20"
                          viewBox="0 0 24 24"
                          fill="currentColor"
                        >
                          <path
                            d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"
                          />
                        </svg>
                      </div>
                      <div class="link-content">
                        <span class="link-title">LinkedIn</span>
                        <span class="link-subtitle">Connect</span>
                      </div>
                      <div class="link-arrow">→</div>
                    </a>

                    <a
                      href="https://github.com/redaelsayied"
                      class="social-link github"
                      target="_blank"
                      rel="noopener noreferrer"
                      aria-label="GitHub Profile - View my code repositories"
                      data-platform="GitHub"
                    >
                      <div class="link-background"></div>
                      <div class="link-icon">
                        <svg
                          width="20"
                          height="20"
                          viewBox="0 0 24 24"
                          fill="currentColor"
                        >
                          <path
                            d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"
                          />
                        </svg>
                      </div>
                      <div class="link-content">
                        <span class="link-title">GitHub</span>
                        <span class="link-subtitle">Follow</span>
                      </div>
                      <div class="link-arrow">→</div>
                    </a>

                    <a
                      href="mailto:<EMAIL>"
                      class="social-link email"
                      aria-label="Email Contact - Get in touch"
                      data-platform="Email"
                    >
                      <div class="link-background"></div>
                      <div class="link-icon">
                        <svg
                          width="20"
                          height="20"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          stroke-width="2"
                        >
                          <path
                            d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"
                          ></path>
                          <polyline points="22,6 12,13 2,6"></polyline>
                        </svg>
                      </div>
                      <div class="link-content">
                        <span class="link-title">Email</span>
                        <span class="link-subtitle">Contact</span>
                      </div>
                      <div class="link-arrow">→</div>
                    </a>

                    <a
                      href="https://twitter.com/redaelsayied"
                      class="social-link twitter"
                      target="_blank"
                      rel="noopener noreferrer"
                      aria-label="Twitter Profile - Latest updates"
                      data-platform="Twitter"
                    >
                      <div class="link-background"></div>
                      <div class="link-icon">
                        <svg
                          width="20"
                          height="20"
                          viewBox="0 0 24 24"
                          fill="currentColor"
                        >
                          <path
                            d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"
                          />
                        </svg>
                      </div>
                      <div class="link-content">
                        <span class="link-title">Twitter</span>
                        <span class="link-subtitle">Follow</span>
                      </div>
                      <div class="link-arrow">→</div>
                    </a>
                  </div>
                </div>

                <!-- Developer Call to Action -->
                <div class="developer-cta">
                  <div class="cta-icon-wrapper">
                    <img
                      src="../icons/developer.jpg"
                      alt="Developer"
                      class="developer-avatar-cta"
                    />
                  </div>
                  <div class="cta-text-content">
                    <h3 class="cta-title">Let's Build Something Great!</h3>
                    <p class="cta-description">
                      Have an idea or a project in mind? I'm always open to
                      collaboration and new opportunities.
                    </p>
                  </div>
                  <a
                    href="mailto:<EMAIL>"
                    class="cta-button-new"
                  >
                    <span>Get in Touch</span>
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                      class="arrow-icon"
                    >
                      <path
                        fill-rule="evenodd"
                        d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z"
                        clip-rule="evenodd"
                      />
                    </svg>
                  </a>
                </div>
              </div>
            </div>
          </div>

          <!-- Version & Info Card -->
          <div class="about-card-row">
            <div class="about-card version-card-simple">
              <div class="card-content">
                <h3>📦 Version Information</h3>

                <div class="version-main-info">
                  <div class="version-number">
                    <span class="version-label">Current Version:</span>
                    <span class="version-value" id="extensionVersion2"
                      >v2.0.0</span
                    >
                    <span class="version-status">Stable</span>
                  </div>

                  <div class="version-details">
                    <div class="detail-row">
                      <span class="detail-label">Released:</span>
                      <span class="detail-value" id="lastUpdated"
                        >June 10, 2025</span
                      >
                    </div>
                    <div class="detail-row">
                      <span class="detail-label">License:</span>
                      <span class="detail-value">MIT License</span>
                    </div>
                    <div class="detail-row">
                      <span class="detail-label">Status:</span>
                      <span class="detail-value status-active">
                        <span class="status-dot-simple"></span>
                        Active & Stable
                      </span>
                    </div>
                  </div>
                </div>

                <div class="version-progress-simple">
                  <div class="progress-label-simple">
                    <span>Reliability Score</span>
                    <span class="progress-value-simple">98%</span>
                  </div>
                  <div class="progress-bar-simple">
                    <div class="progress-fill-simple" style="width: 98%"></div>
                  </div>
                </div>

                <div class="version-actions-simple">
                  <button
                    class="btn-simple btn-primary-simple"
                    id="checkUpdateBtn"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                      width="16"
                      height="16"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                      />
                    </svg>
                    Check Updates
                  </button>
                  <button
                    class="btn-simple btn-secondary-simple"
                    id="viewChangelogBtn"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                      width="16"
                      height="16"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                      />
                    </svg>
                    Changelog
                  </button>
                </div>
              </div>
            </div>

            <!-- User Testimonial Card -->
            <div class="about-card testimonial-card interactive">
              <div class="card-content">
                <h3>💬 What Users Say</h3>
                <div
                  class="testimonial-carousel"
                  id="testimonialCarousel"
                  aria-live="polite"
                  aria-label="User testimonials"
                >
                  <div class="testimonial active">
                    <div class="testimonial-profile">
                      <div class="profile-avatar">🎨</div>
                      <div class="profile-info">
                        <span class="profile-name">Abdo Awad</span>
                        <span class="profile-title">Front-end Developer</span>
                      </div>
                    </div>
                    <p class="testimonial-text">
                      "This extension is incredibly useful! It saves me so much
                      time when working with SVGs. The quality is outstanding
                      and the interface is super intuitive."
                    </p>
                    <div class="testimonial-meta">
                      <span class="verified-badge">✓ Verified User</span>
                      <span class="review-date">3 weeks ago</span>
                    </div>
                  </div>
                  <div class="testimonial">
                    <div class="testimonial-profile">
                      <div class="profile-avatar">👨‍💻</div>
                      <div class="profile-info">
                        <span class="profile-name">Osama Gasser</span>
                        <span class="profile-title">Back-end Developer</span>
                      </div>
                    </div>
                    <p class="testimonial-text">
                      "The best SVG converter extension I've used. Simple yet
                      powerful! Perfect for my daily workflow."
                    </p>
                    <div class="testimonial-meta">
                      <span class="verified-badge">✓ Verified User</span>
                      <span class="review-date">1 week ago</span>
                    </div>
                  </div>
                  <div class="testimonial">
                    <div class="testimonial-profile">
                      <div class="profile-avatar">🎯</div>
                      <div class="profile-info">
                        <span class="profile-name">Mohamed El-Sayed</span>
                        <span class="profile-title">Graphic Designer</span>
                      </div>
                    </div>

                    <p class="testimonial-text">
                      "Great tool for quick SVG conversions. The batch
                      processing feature is amazing and saves hours of work."
                    </p>
                    <div class="testimonial-meta">
                      <span class="verified-badge">✓ Verified User</span>
                      <span class="review-date">3 days ago</span>
                    </div>
                  </div>
                  <div
                    class="testimonial-nav"
                    role="tablist"
                    aria-label="Testimonial navigation"
                  >
                    <span
                      class="testimonial-dot active"
                      data-index="0"
                      role="tab"
                      aria-selected="true"
                      aria-label="Testimonial 1"
                    ></span>
                    <span
                      class="testimonial-dot"
                      data-index="1"
                      role="tab"
                      aria-selected="false"
                      aria-label="Testimonial 2"
                    ></span>
                    <span
                      class="testimonial-dot"
                      data-index="2"
                      role="tab"
                      aria-selected="false"
                      aria-label="Testimonial 3"
                    ></span>
                  </div>
                  <div class="carousel-controls">
                    <button
                      class="carousel-btn prev-btn"
                      aria-label="Previous testimonial"
                    >
                      ❮
                    </button>
                    <button
                      class="carousel-btn next-btn"
                      aria-label="Next testimonial"
                    >
                      ❯
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!-- Ultra Modern Support & Feedback Card -->
          <div class="about-card support-card">
            <!-- Animated Background Elements -->
            <div class="support-bg-animation">
              <div class="floating-shape shape-1"></div>
              <div class="floating-shape shape-2"></div>
              <div class="floating-shape shape-3"></div>
            </div>

            <!-- Hero Section -->
            <div class="support-hero">
              <div class="support-hero-content">
                <div class="support-badge">
                  <span class="badge-text">Community</span>
                  <div class="badge-glow"></div>
                </div>
                <h3 class="support-title">
                  <span class="title-highlight">Support</span> & Feedback
                </h3>
                <p class="support-tagline">
                  Your voice shapes the future of SVG2PNG
                </p>
              </div>
              <div class="support-hero-visual">
                <div class="hero-circle">
                  <svg
                    class="hero-icon"
                    width="40"
                    height="40"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                  >
                    <path
                      d="M3 11v3a1 1 0 0 0 1 1h1a1 1 0 0 0 1-1v-3a1 1 0 0 0-1-1H4a1 1 0 0 0-1 1Z"
                    ></path>
                    <path
                      d="M21 11v3a1 1 0 0 1-1 1h-1a1 1 0 0 1-1-1v-3a1 1 0 0 1 1-1h1a1 1 0 0 1 1 1Z"
                    ></path>
                    <path d="M3 13a9 9 0 0 1 9-9 9 9 0 0 1 9 9"></path>
                    <path d="M18 13v4a2 2 0 0 1-2 2h-4a2 2 0 0 1-2-2v-1"></path>
                  </svg>
                  <div class="hero-ring ring-1"></div>
                  <div class="hero-ring ring-2"></div>
                  <div class="hero-ring ring-3"></div>
                </div>
              </div>
            </div>

            <!-- Interactive Action Grid -->
            <div class="support-grid">
              <div class="support-action-card bug-card">
                <div class="action-header">
                  <div class="action-icon bug-icon">
                    <svg
                      width="20"
                      height="20"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                    >
                      <path d="m8 2 1.88 1.88"></path>
                      <path d="M14.12 3.88 16 2"></path>
                      <path d="M9 7.13v-1a3.003 3.003 0 1 1 6 0v1"></path>
                      <path
                        d="M12 20c-3.3 0-6-2.7-6-6v-3a4 4 0 0 1 4-4h4a4 4 0 0 1 4 4v3c0 3.3-2.7 6-6 6"
                      ></path>
                      <path d="M12 20v-9"></path>
                      <path d="M6.53 9C4.6 8.8 3 7.1 3 5"></path>
                      <path d="M6 13H2"></path>
                      <path d="M3 21c0-2.1 1.7-3.9 3.8-4"></path>
                      <path d="M20.97 5c0 2.1-1.6 3.8-3.5 4"></path>
                      <path d="M22 13h-4"></path>
                      <path d="M17.2 17c2.1.1 3.8 1.9 3.8 4"></path>
                    </svg>
                  </div>
                  <h4>Found a Bug?</h4>
                </div>
                <p class="action-description">
                  Help us squash bugs and improve stability
                </p>
                <a
                  href="https://docs.google.com/forms/d/e/1FAIpQLSf8ji8RXR0F1sevT2IIMkGGH3iJOzW_RRtpqGKkW_ozRIa9-w/viewform?usp=dialog"
                  class="action-btn bug-btn"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <span class="btn-content">
                    <span class="btn-label">Report Issue</span>
                    <svg
                      class="btn-arrow"
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                    >
                      <path d="M7 17l9.2-9.2M17 17V7H7"></path>
                    </svg>
                  </span>
                  <div class="btn-glow"></div>
                </a>
              </div>

              <div class="support-action-card feature-card">
                <div class="action-header">
                  <div class="action-icon idea-icon">
                    <svg
                      width="20"
                      height="20"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                    >
                      <path
                        d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 1 1 7.072 0l-.548.547A3.374 3.374 0 0 0 14 18.469V19a2 2 0 0 1-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"
                      ></path>
                    </svg>
                  </div>
                  <h4>Got an Idea?</h4>
                </div>
                <p class="action-description">
                  Share your brilliant feature suggestions
                </p>
                <a
                  href="https://docs.google.com/forms/d/e/1FAIpQLSdSYrSt3rpKoX221y5_PLbdPB4hWGclsROU_wiKylEzKUpktA/viewform?usp=dialog"
                  class="action-btn feature-btn"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <span class="btn-content">
                    <span class="btn-label">Suggest Feature</span>
                    <svg
                      class="btn-arrow"
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                    >
                      <path d="M7 17l9.2-9.2M17 17V7H7"></path>
                    </svg>
                  </span>
                  <div class="btn-glow"></div>
                </a>
              </div>

              <div class="support-action-card love-card">
                <div class="action-header">
                  <div class="action-icon love-icon">
                    <svg
                      width="20"
                      height="20"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                    >
                      <path
                        d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"
                      ></path>
                    </svg>
                  </div>
                  <h4>Show Some Love?</h4>
                </div>
                <p class="action-description">
                  Rate us and help others discover SVG2PNG
                </p>
                <a
                  href="https://chrome.google.com/webstore/detail/svg2png/review"
                  class="action-btn love-btn primary"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <span class="btn-content">
                    <span class="btn-label">Rate Extension</span>
                    <svg
                      class="btn-arrow"
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                    >
                      <path d="M7 17l9.2-9.2M17 17V7H7"></path>
                    </svg>
                  </span>
                  <div class="btn-glow"></div>
                </a>
              </div>
            </div>
            <!-- Direct Connection Section -->
            <div class="support-footer">
              <div class="direct-connect">
                <div class="connect-header">
                  <h4>Connect Directly</h4>
                  <p>
                    Get in touch with the developer for immediate assistance
                  </p>
                </div>
                <div class="connect-options">
                  <a
                    href="mailto:<EMAIL>"
                    class="connect-btn email-connect"
                  >
                    <div class="connect-icon">
                      <svg
                        width="20"
                        height="20"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        stroke-width="2"
                      >
                        <path
                          d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"
                        ></path>
                        <polyline points="22,6 12,13 2,6"></polyline>
                      </svg>
                    </div>
                    <div class="connect-info">
                      <span class="connect-label">Email Support</span>
                      <span class="connect-detail"
                        ><EMAIL></span
                      >
                    </div>
                  </a>
                  <a
                    href="https://www.linkedin.com/in/redaelsayed/"
                    class="connect-btn linkedin-connect"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    <div class="connect-icon">
                      <svg
                        width="20"
                        height="20"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        stroke-width="2"
                      >
                        <path
                          d="M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z"
                        ></path>
                        <rect x="2" y="9" width="4" height="12"></rect>
                        <circle cx="4" cy="4" r="2"></circle>
                      </svg>
                    </div>
                    <div class="connect-info">
                      <span class="connect-label">LinkedIn Profile</span>
                      <span class="connect-detail">@redaelsayed</span>
                    </div>
                  </a>
                </div>
                <div class="response-time">
                  <div class="response-indicator">
                    <div class="status-dot"></div>
                    <span>Usually responds within 24 hours</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Libraries & Resources Card -->
          <div class="about-card resources-card">
            <div class="card-content">
              <h3>Powered By</h3>
              <p class="resource-description">
                Special thanks to the open-source community and the following
                libraries that make SVG2PNG possible:
              </p>
              <div class="resource-list">
                <div class="resource-item">
                  <div class="resource-icon">
                    <svg
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                    >
                      <path
                        d="M20.59 13.41l-7.17 7.17a2 2 0 0 1-2.83 0L2 12V2h10l8.59 8.59a2 2 0 0 1 0 2.82z"
                      ></path>
                      <line x1="7" y1="7" x2="7.01" y2="7"></line>
                    </svg>
                  </div>
                  <div class="resource-details">
                    <h5>JSZip</h5>
                    <p>
                      For creating and managing ZIP archives of converted files
                    </p>
                  </div>
                </div>
                <div class="resource-item">
                  <div class="resource-icon">
                    <svg
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                    >
                      <rect
                        x="3"
                        y="3"
                        width="18"
                        height="18"
                        rx="2"
                        ry="2"
                      ></rect>
                      <circle cx="8.5" cy="8.5" r="1.5"></circle>
                      <polyline points="21 15 16 10 5 21"></polyline>
                    </svg>
                  </div>
                  <div class="resource-details">
                    <h5>Canvas API</h5>
                    <p>For high-quality image conversion and manipulation</p>
                  </div>
                </div>
                <div class="resource-item">
                  <div class="resource-icon">
                    <svg
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                    >
                      <polyline points="16 18 22 12 16 6"></polyline>
                      <polyline points="8 6 2 12 8 18"></polyline>
                    </svg>
                  </div>
                  <div class="resource-details">
                    <h5>Browser Extension APIs</h5>
                    <p>For seamless browser integration and functionality</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
      </main>
    </div>

    <!-- Status Messages -->
    <div
      id="statusMessage"
      class="status-message"
      role="alert"
      aria-live="polite"
    ></div>

    <!-- Modal for confirmations -->
    <div id="confirmModal" class="modal" role="dialog" aria-hidden="true">
      <div class="modal-content">
        <h3 id="modalTitle">Confirm Action</h3>
        <p id="modalMessage">Are you sure?</p>
        <div class="modal-actions">
          <button id="modalCancelBtn" class="btn btn-secondary">Cancel</button>
          <button id="modalConfirmBtn" class="btn btn-primary">Confirm</button>
        </div>
      </div>
    </div>

    <script type="module" src="entries/settings.js"></script>
    <script src="src/settings/about.js"></script>
  </body>
</html>
