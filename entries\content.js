/**
 * SVG Switcher Extension - Content Script (Refactored)
 * Modern modular architecture with improved maintainability
 */

(function () {
  "use strict";

  let orchestrator = null;
  let initializationPromise = null;
  let EXTENSION_CONFIG = null;
  let DEBUG = null;

  /**
   * Initialize the content script with error handling
   */
  async function initialize() {
    if (initializationPromise) {
      return initializationPromise;
    }

    initializationPromise = (async () => {
      try {
        // Dynamic import of modules
        const { initializeContentScript, cleanupContentScript } = await import(
          chrome.runtime.getURL("src/content/content-orchestrator.js")
        );
        const constants = await import(
          chrome.runtime.getURL("src/config/constants.js")
        );
        const { logDebug, logError } = await import(
          chrome.runtime.getURL("src/utils/logger.utils.js")
        );

        EXTENSION_CONFIG = constants.EXTENSION_CONFIG;
        DEBUG = constants.DEBUG;

        logDebug("Starting content script initialization...");

        // Wait for DOM to be ready
        await waitForDOM();

        // Initialize the orchestrator
        orchestrator = await initializeContentScript();

        logDebug("Content script ready");

        return orchestrator;
      } catch (error) {
        console.error(`SVG2PNG: Initialization failed:`, error);
        throw error;
      }
    })();

    return initializationPromise;
  }

  /**
   * Wait for DOM to be ready
   * @returns {Promise<void>}
   */
  function waitForDOM() {
    return new Promise((resolve) => {
      if (document.readyState === "loading") {
        document.addEventListener("DOMContentLoaded", resolve, { once: true });
      } else {
        resolve();
      }
    });
  }

  /**
   * Handle cleanup on page unload
   */
  async function handleCleanup() {
    try {
      if (DEBUG?.enabled) {
        const { logDebug } = await import(
          chrome.runtime.getURL("src/utils/logger.utils.js")
        );
        logDebug("Page unloading, cleaning up...");
      }

      // Cleanup via dynamic import
      import(chrome.runtime.getURL("src/content/content-orchestrator.js"))
        .then(({ cleanupContentScript }) => {
          cleanupContentScript();
        })
        .catch(console.error);

      orchestrator = null;
      initializationPromise = null;
    } catch (error) {
      console.error(
        `${EXTENSION_CONFIG?.logPrefix || "SVG2PNG:"} Cleanup error:`,
        error
      );
    }
  }

  /**
   * Handle SPA navigation (for single-page applications)
   */
  function handleSPANavigation() {
    // Detect URL changes (for SPAs that don't trigger full page reloads)
    let currentURL = window.location.href;

    const urlObserver = async () => {
      if (window.location.href !== currentURL) {
        currentURL = window.location.href;

        if (DEBUG?.enabled) {
          try {
            const { logDebug } = await import(
              chrome.runtime.getURL("src/utils/logger.utils.js")
            );
            logDebug("URL changed, reinitializing...");
          } catch (e) {
            console.log(
              `${
                EXTENSION_CONFIG?.logPrefix || "SVG2PNG:"
              } URL changed, reinitializing...`
            );
          }
        }

        // Reinitialize for new page content
        setTimeout(async () => {
          try {
            if (orchestrator) {
              await orchestrator.reinitialize();
            } else {
              await initialize();
            }
          } catch (error) {
            console.error(
              `${
                EXTENSION_CONFIG?.logPrefix || "SVG2PNG:"
              } SPA reinitialize error:`,
              error
            );
          }
        }, 100);
      }
    };

    // Use multiple methods to detect SPA navigation
    window.addEventListener("popstate", urlObserver);
    window.addEventListener("pushstate", urlObserver);
    window.addEventListener("replacestate", urlObserver);

    // Also observe for DOM changes that might indicate new page content
    const domObserver = new MutationObserver((mutations) => {
      // Check if significant DOM changes occurred
      const hasSignificantChanges = mutations.some(
        (mutation) =>
          mutation.type === "childList" &&
          mutation.addedNodes.length > 0 &&
          Array.from(mutation.addedNodes).some(
            (node) =>
              node.nodeType === Node.ELEMENT_NODE &&
              (node.tagName === "MAIN" ||
                node.tagName === "ARTICLE" ||
                node.tagName === "SECTION")
          )
      );

      if (hasSignificantChanges) {
        urlObserver();
      }
    });

    // Start observing
    domObserver.observe(document.body, {
      childList: true,
      subtree: true,
    });

    // Cleanup observers
    window.addEventListener("beforeunload", () => {
      domObserver.disconnect();
      window.removeEventListener("popstate", urlObserver);
      window.removeEventListener("pushstate", urlObserver);
      window.removeEventListener("replacestate", urlObserver);
    });
  }

  /**
   * Set up error handling
   */
  function setupErrorHandling() {
    // Global error handler
    window.addEventListener("error", (event) => {
      if (event.filename && event.filename.includes("content")) {
        console.error(
          `${EXTENSION_CONFIG?.logPrefix || "SVG2PNG:"} Global error:`,
          event.error
        );
      }
    });

    // Unhandled promise rejection handler
    window.addEventListener("unhandledrejection", (event) => {
      if (
        event.reason &&
        event.reason.stack &&
        event.reason.stack.includes("svg2png")
      ) {
        console.error(
          `${
            EXTENSION_CONFIG?.logPrefix || "SVG2PNG:"
          } Unhandled promise rejection:`,
          event.reason
        );
      }
    });
  }

  /**
   * Main entry point
   */
  async function main() {
    try {
      // Set up error handling first
      setupErrorHandling();

      // Set up cleanup handlers
      window.addEventListener("beforeunload", handleCleanup);

      // Set up SPA navigation handling
      handleSPANavigation();

      // Initialize the content script
      await initialize();
    } catch (error) {
      console.error(
        `${
          EXTENSION_CONFIG?.logPrefix || "SVG2PNG:"
        } Main initialization error:`,
        error
      );
    }
  }

  // Start the content script
  main();

  // Expose for debugging in development only
  if (typeof window !== "undefined") {
    setTimeout(() => {
      if (DEBUG?.enabled) {
        window.svg2pngOrchestrator = () => orchestrator;
      }
    }, 1000);
  }
})();
