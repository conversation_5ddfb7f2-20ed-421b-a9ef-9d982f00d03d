/**
 * Initialization Utilities
 * Shared patterns for entry point initialization
 */

import { logDebug, logError } from "./logger.utils.js";
import { initializeAccessibility } from "./accessibility.utils.js";
import { preloadCriticalModules } from "./lazy-loader.utils.js";

/**
 * Standard initialization options
 */
export const INIT_OPTIONS = {
  preloadModules: true,
  initAccessibility: true,
  setupErrorHandling: true,
  exposeForDebug: false,
};

/**
 * Initialize common features for entry points
 * @param {string} context - Context name (e.g., "popup", "settings", "content")
 * @param {Object} options - Initialization options
 * @returns {Promise<void>}
 */
export async function initializeBase(context, options = {}) {
  const config = { ...INIT_OPTIONS, ...options };

  logDebug(`${context} initializing...`);

  // Setup global error handling if requested
  if (config.setupErrorHandling) {
    setupGlobalErrorHandlers(context);
  }

  // Preload critical modules if requested
  if (config.preloadModules) {
    try {
      await preloadCriticalModules();
      logDebug("Critical modules preloaded", context);
    } catch (error) {
      logError(`Failed to preload modules: ${error.message}`, context);
      throw new Error(`Module preload failed: ${error.message}`);
    }
  }

  // Initialize accessibility if requested
  if (config.initAccessibility) {
    try {
      initializeAccessibility(document);
      logDebug("Accessibility initialized", context);
    } catch (error) {
      logError(`Failed to initialize accessibility: ${error.message}`, context);
      // Don't throw - accessibility is important but not critical for basic functionality
    }
  }

  logDebug(`${context} base initialization complete`);
}

/**
 * Setup global error handlers for an entry point
 * @param {string} context - Context name
 */
function setupGlobalErrorHandlers(context) {
  // Global error handler
  window.addEventListener("error", (event) => {
    if (event.filename && event.filename.includes(context.toLowerCase())) {
      logError(`Global error: ${event.error}`, context);
    }
  });

  // Unhandled promise rejection handler
  window.addEventListener("unhandledrejection", (event) => {
    if (
      event.reason &&
      event.reason.stack &&
      event.reason.stack.includes(context.toLowerCase())
    ) {
      logError(`Unhandled promise rejection: ${event.reason}`, context);
    }
  });
}

/**
 * Handle initialization error with user-friendly feedback
 * @param {Error} error - Error that occurred
 * @param {string} context - Context where error occurred
 * @param {string} statusElementId - ID of status element to update
 */
export function handleInitError(error, context, statusElementId = "status") {
  logError(`${context} initialization error: ${error.message}`, context);

  // Show user-friendly error message
  const status = document.getElementById(statusElementId);
  if (status) {
    status.className = "status error";
    status.textContent = `Error initializing ${context.toLowerCase()}. Please try refreshing.`;
  }
}

/**
 * Setup cleanup handler for entry point
 * @param {string} context - Context name
 * @param {Function} cleanupFn - Cleanup function to call
 */
export function setupCleanupHandler(context, cleanupFn) {
  window.addEventListener("beforeunload", () => {
    logDebug(`${context} unloading...`);
    if (cleanupFn) {
      try {
        cleanupFn();
      } catch (error) {
        logError(`Cleanup error: ${error.message}`, context);
      }
    }
  });
}
