/**
 * Header Enhancer for Settings Page
 * Handles dynamic header effects and animations
 */

/**
 * Header enhancement class for settings page visual effects
 */
export class HeaderEnhancer {
  constructor() {
    this.isInitialized = false;
    this.cachedElements = new Map();
    this.animationFrame = null;
    this.particles = [];
    this.mouse = { x: 0, y: 0 };
  }

  /**
   * Get element with caching
   * @param {string} selector - CSS selector
   * @param {boolean} useCache - Whether to use cache
   * @returns {HTMLElement|null} Element or null
   */
  getElement(selector, useCache = true) {
    if (useCache && this.cachedElements.has(selector)) {
      return this.cachedElements.get(selector);
    }

    const element = document.querySelector(selector);
    if (useCache && element) {
      this.cachedElements.set(selector, element);
    }
    return element;
  }

  /**
   * Get elements with caching
   * @param {string} selector - CSS selector
   * @param {boolean} useCache - Whether to use cache
   * @returns {NodeList} Elements
   */
  getElements(selector, useCache = true) {
    if (useCache && this.cachedElements.has(`${selector}:all`)) {
      return this.cachedElements.get(`${selector}:all`);
    }

    const elements = document.querySelectorAll(selector);
    if (useCache && elements.length > 0) {
      this.cachedElements.set(`${selector}:all`, elements);
    }
    return elements;
  }

  /**
   * Initialize header enhancements
   */
  init() {
    if (this.isInitialized) return;

    try {
      this.generateDynamicElements();
      this.setupEventListeners();
      this.startAnimationLoop();
      this.isInitialized = true;
    } catch (error) {
      console.error("Header enhancer initialization failed:", error);
    }
  }

  /**
   * Generate dynamic visual elements
   */
  generateDynamicElements() {
    this.generatePatternElements();
    this.generateParticleElements();
    this.generateStarElements();
    this.generateSparkleElements();
  }

  /**
   * Generate pattern elements
   */
  generatePatternElements() {
    const header = this.getElement(".settings-header");
    if (!header) return;

    // Create pattern overlay
    const pattern = document.createElement("div");
    pattern.className = "header-pattern";
    pattern.innerHTML = Array(20)
      .fill()
      .map(() => `<div class="pattern-dot"></div>`)
      .join("");

    header.appendChild(pattern);
  }

  /**
   * Generate particle elements
   */
  generateParticleElements() {
    const header = this.getElement(".settings-header");
    if (!header) return;

    const particleContainer = document.createElement("div");
    particleContainer.className = "particle-container";

    for (let i = 0; i < 15; i++) {
      const particle = document.createElement("div");
      particle.className = "header-particle";
      particle.style.left = `${Math.random() * 100}%`;
      particle.style.top = `${Math.random() * 100}%`;
      particle.style.animationDelay = `${Math.random() * 3}s`;
      particleContainer.appendChild(particle);
    }

    header.appendChild(particleContainer);
  }

  /**
   * Generate star elements
   */
  generateStarElements() {
    const header = this.getElement(".settings-header");
    if (!header) return;

    const starContainer = document.createElement("div");
    starContainer.className = "star-container";

    for (let i = 0; i < 8; i++) {
      const star = document.createElement("div");
      star.className = "header-star";
      star.style.left = `${Math.random() * 100}%`;
      star.style.top = `${Math.random() * 100}%`;
      starContainer.appendChild(star);
    }

    header.appendChild(starContainer);
  }

  /**
   * Generate sparkle elements
   */
  generateSparkleElements() {
    const header = this.getElement(".settings-header");
    if (!header) return;

    const sparkleContainer = document.createElement("div");
    sparkleContainer.className = "sparkle-container";

    for (let i = 0; i < 12; i++) {
      const sparkle = document.createElement("div");
      sparkle.className = "header-sparkle";
      sparkle.style.left = `${Math.random() * 100}%`;
      sparkle.style.top = `${Math.random() * 100}%`;
      sparkleContainer.appendChild(sparkle);
    }

    header.appendChild(sparkleContainer);
  }

  /**
   * Setup event listeners
   */
  setupEventListeners() {
    document.addEventListener("mousemove", (e) => {
      this.mouse.x = e.clientX;
      this.mouse.y = e.clientY;
      this.updateParallaxEffects();
    });

    this.setupInteractiveElements();
  }

  /**
   * Setup interactive elements
   */
  setupInteractiveElements() {
    const interactiveElements = this.getElements(
      ".nav-link, .btn, .form-control"
    );

    interactiveElements.forEach((element) => {
      element.addEventListener("mouseenter", (e) =>
        this.handleInteraction(element, "enter", e)
      );
      element.addEventListener("mouseleave", (e) =>
        this.handleInteraction(element, "leave", e)
      );
      element.addEventListener("click", (e) =>
        this.handleInteraction(element, "click", e)
      );
    });
  }

  /**
   * Handle element interactions
   * @param {HTMLElement} element - Target element
   * @param {string} event - Event type
   * @param {Event} e - Event object
   */
  handleInteraction(element, event, e) {
    switch (event) {
      case "enter":
        element.classList.add("enhanced-hover");
        this.createButtonParticles(element);
        break;
      case "leave":
        element.classList.remove("enhanced-hover");
        break;
      case "click":
        this.createRippleEffect(element, e);
        this.activateSparkles();
        break;
    }
  }

  /**
   * Setup particle system
   */
  setupParticleSystem() {
    this.particles = Array(10)
      .fill()
      .map(() => ({
        x: Math.random() * window.innerWidth,
        y: Math.random() * window.innerHeight,
        vx: (Math.random() - 0.5) * 2,
        vy: (Math.random() - 0.5) * 2,
        life: 1,
      }));
  }

  /**
   * Update parallax effects
   */
  updateParallaxEffects() {
    const header = this.getElement(".settings-header");
    if (!header) return;

    const rect = header.getBoundingClientRect();
    const centerX = rect.left + rect.width / 2;
    const centerY = rect.top + rect.height / 2;

    const deltaX = (this.mouse.x - centerX) / rect.width;
    const deltaY = (this.mouse.y - centerY) / rect.height;

    // Apply parallax to particles
    const particles = this.getElements(".header-particle");
    particles.forEach((particle, index) => {
      const depth = (index % 3) + 1;
      const moveX = deltaX * depth * 10;
      const moveY = deltaY * depth * 10;
      this.applyTransform(particle, `translate(${moveX}px, ${moveY}px)`);
    });

    // Apply parallax to stars
    const stars = this.getElements(".header-star");
    stars.forEach((star, index) => {
      const depth = (index % 2) + 1;
      const moveX = deltaX * depth * 15;
      const moveY = deltaY * depth * 15;
      this.applyTransform(star, `translate(${moveX}px, ${moveY}px)`);
    });

    this.updateLightRays();
  }

  /**
   * Update light rays effect
   */
  updateLightRays() {
    const header = this.getElement(".settings-header");
    if (!header) return;

    const intensity = Math.sin(Date.now() * 0.001) * 0.5 + 0.5;
    header.style.setProperty("--light-intensity", intensity);
  }

  /**
   * Setup dynamic effects
   */
  setupDynamicEffects() {
    // Animate particles
    setInterval(() => {
      const particles = this.getElements(".header-particle");
      particles.forEach((particle) => {
        const currentTransform = particle.style.transform || "";
        const rotation = `rotate(${Math.random() * 360}deg)`;
        particle.style.transform = `${currentTransform} ${rotation}`;
      });
    }, 2000);

    // Animate sparkles
    setInterval(() => {
      const sparkles = this.getElements(".header-sparkle");
      sparkles.forEach((sparkle, index) => {
        setTimeout(() => {
          sparkle.classList.add("sparkle-animate");
          setTimeout(() => sparkle.classList.remove("sparkle-animate"), 1000);
        }, index * 200);
      });
    }, 5000);
  }

  /**
   * Create button particles
   * @param {HTMLElement} button - Button element
   */
  createButtonParticles(button) {
    // Implementation for button particle effects
  }

  /**
   * Create ripple effect
   * @param {HTMLElement} button - Button element
   * @param {Event} event - Click event
   */
  createRippleEffect(button, event) {
    // Implementation for ripple effects
  }

  /**
   * Setup title animations
   */
  setupTitleAnimations() {
    const title = this.getElement(".settings-title");
    if (!title) return;

    // Split text into spans for individual letter animation
    const text = title.textContent;
    title.innerHTML = text
      .split("")
      .map(
        (char, index) =>
          `<span class="letter" style="animation-delay: ${
            index * 0.1
          }s">${char}</span>`
      )
      .join("");

    // Trigger animation
    setTimeout(() => {
      title.classList.add("title-animated");
    }, 500);
  }

  /**
   * Activate sparkles
   */
  activateSparkles() {
    const sparkles = this.getElements(".header-sparkle");
    sparkles.forEach((sparkle, index) => {
      setTimeout(() => {
        sparkle.classList.add("sparkle-burst");
        setTimeout(() => sparkle.classList.remove("sparkle-burst"), 800);
      }, index * 100);
    });
  }

  /**
   * Animate particles
   * @param {Array} particles - Particle array
   */
  animateParticles(particles) {
    particles.forEach((particle) => {
      particle.x += particle.vx;
      particle.y += particle.vy;
      particle.life -= 0.01;
    });
  }

  /**
   * Apply transform to element
   * @param {HTMLElement} element - Target element
   * @param {string} transform - Transform string
   */
  applyTransform(element, transform) {
    element.style.transform = transform;
  }

  /**
   * Start animation loop
   */
  startAnimationLoop() {
    const animate = (timestamp) => {
      this.updateParallaxEffects();
      this.updateParticleSystem();
      this.updateDynamicEffects();
      this.animationFrame = requestAnimationFrame(animate);
    };
    this.animationFrame = requestAnimationFrame(animate);
  }

  /**
   * Update parallax effects (animation loop version)
   */
  updateParallaxEffects() {
    // Mouse-based parallax already handled in event listener
    // This is for continuous effects
    const time = Date.now() * 0.001;

    const particles = this.getElements(".header-particle");
    particles.forEach((particle, index) => {
      const floatY = Math.sin(time + index) * 2;
      const currentTransform = particle.style.transform || "";
      const baseTransform = currentTransform.replace(/translateY\([^)]*\)/, "");
      this.applyTransform(particle, `${baseTransform} translateY(${floatY}px)`);
    });

    // Rotate stars slowly
    const stars = this.getElements(".header-star");
    stars.forEach((star, index) => {
      const rotation = (time * 10 + index * 45) % 360;
      this.applyTransform(star, `rotate(${rotation}deg)`);
    });
  }

  /**
   * Update particle system
   */
  updateParticleSystem() {
    // Update any dynamic particle systems here
    if (this.particles.length > 0) {
      this.animateParticles(this.particles);

      // Remove dead particles
      this.particles = this.particles.filter((p) => p.life > 0);
    }
  }

  /**
   * Calculate mouse influence on particles
   * @param {Object} particle - Particle object
   * @returns {Object} Influence factors
   */
  calculateMouseInfluence(particle) {
    const distance = Math.sqrt(
      Math.pow(particle.x - this.mouse.x, 2) +
        Math.pow(particle.y - this.mouse.y, 2)
    );

    const maxDistance = 200;
    const influence = Math.max(0, 1 - distance / maxDistance);

    return {
      x: (this.mouse.x - particle.x) * influence * 0.01,
      y: (this.mouse.y - particle.y) * influence * 0.01,
      scale: 1 + influence * 0.2,
    };
  }

  /**
   * Update dynamic effects
   */
  updateDynamicEffects() {
    const time = Date.now() * 0.001;

    // Pulse effect for sparkles
    const sparkles = this.getElements(".header-sparkle");
    sparkles.forEach((sparkle, index) => {
      const pulse = Math.sin(time * 2 + index) * 0.3 + 0.7;
      sparkle.style.opacity = pulse;
    });

    // Wave effect for pattern dots
    const dots = this.getElements(".pattern-dot");
    dots.forEach((dot, index) => {
      const wave = Math.sin(time + index * 0.5) * 0.5 + 0.5;
      dot.style.transform = `scale(${0.5 + wave * 0.5})`;
    });
  }

  /**
   * Enhance theme transitions
   */
  enhanceThemeTransitions() {
    // Add smooth transitions for theme changes
    const root = document.documentElement;
    root.style.transition = "all 0.3s ease";

    // Watch for theme changes
    const observer = new MutationObserver(() => {
      this.updateEffectsForTheme();
    });

    observer.observe(root, {
      attributes: true,
      attributeFilter: ["class", "data-theme"],
    });
  }

  /**
   * Destroy the enhancer and clean up resources
   */
  destroy() {
    if (this.animationFrame) {
      cancelAnimationFrame(this.animationFrame);
    }

    // Remove generated elements
    const dynamicElements = this.getElements(
      ".particle-container, .star-container, .sparkle-container, .header-pattern"
    );
    dynamicElements.forEach((element) => {
      element.remove();
    });

    this.cachedElements.clear();
    this.particles = [];
    this.isInitialized = false;
  }
}
