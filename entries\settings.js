/**
 * Settings Page Controller
 * Manages all settings page functionality and data persistence
 */

import { StorageService } from "../src/services/storage.service.js";
import {
  EXTENSION_CONFIG,
  DEBUG,
  DEFAULT_SETTINGS,
} from "../src/config/constants.js";
import {
  initializeAccessibility,
  announceToScreenReader,
} from "../src/utils/accessibility.utils.js";

/**
 * Settings page controller class - Enhanced with better organization
 */
class SettingsController {
  constructor() {
    this.currentSection = "general";
    this.settings = {};
    this.isLoading = false;
    this.autoSaveTimeout = null;
    this.eventHandlers = {}; // Store event handlers for easy cleanup
  }

  /**
   * Initialize the settings page
   */
  async init() {
    try {
      this.logDebug("Settings page initializing...");

      // Initialize accessibility
      initializeAccessibility(document);

      // Load current settings
      await this.loadSettings();

      // Set up event listeners and UI
      this.setupAllEventListeners();
      this.initializeUI();

      // Load presets
      await this.loadPresets();

      this.logDebug("Settings page initialized");
    } catch (error) {
      this.handleError("Settings initialization error:", error);
      this.showStatus("Failed to initialize settings page", "error");
    }
  }

  /**
   * Load settings from storage
   */
  async loadSettings() {
    try {
      const stored = await StorageService.get(["settings"], "sync");
      this.settings = { ...DEFAULT_SETTINGS, ...stored.settings };
      this.populateForm();
      // Mark form as clean on initial load
      this.markFormAsSaved();
    } catch (error) {
      this.handleError("Error loading settings:", error);
      this.settings = DEFAULT_SETTINGS;
      this.populateForm();
      // Mark form as clean even with default settings
      this.markFormAsSaved();
    }
  }

  /**
   * Get default settings object (now uses centralized defaults)
   */
  getDefaultSettings() {
    return { ...DEFAULT_SETTINGS };
  }

  /**
   * Set up all event listeners in a centralized way
   */
  setupAllEventListeners() {
    // Group all event listener setups
    const setups = [
      {
        name: "navigation",
        fn: this.setupNavigationListeners.bind(this),
      },
      {
        name: "buttons",
        fn: this.setupButtonListeners.bind(this),
      },
      {
        name: "rangeInputs",
        fn: this.setupRangeInputs.bind(this),
      },
      {
        name: "formChanges",
        fn: this.setupFormChangeListeners.bind(this),
      },
      {
        name: "presets",
        fn: this.setupPresetEventListeners.bind(this),
      },
      {
        name: "backupRestore",
        fn: this.setupBackupRestoreListeners.bind(this),
      },
      {
        name: "modal",
        fn: this.setupModal.bind(this),
      },
      {
        name: "fab",
        fn: this.setupFloatingActionButton.bind(this),
      },
    ];

    // Execute each setup function and track for potential cleanup
    setups.forEach((setup) => {
      this.eventHandlers[setup.name] = setup.fn() || [];
    });
  }
  /**
   * Set up navigation event listeners
   */
  setupNavigationListeners() {
    const handlers = [];
    document.querySelectorAll(".nav-link").forEach((link) => {
      const handler = (e) => {
        const section = link.getAttribute("data-section");
        // Only handle navigation links with data-section attribute
        if (section) {
          e.preventDefault();
          e.stopPropagation(); // Prevent other handlers from running
          this.showSection(section);
        }
      };

      link.addEventListener("click", handler);
      handlers.push({ element: link, event: "click", handler });
    });
    return handlers;
  }

  /**
   * Set up main button listeners
   */
  setupButtonListeners() {
    const handlers = [];

    // Save settings
    const saveBtn = document.getElementById("saveSettingsBtn");
    const saveHandler = () => this.saveAllSettings();
    saveBtn.addEventListener("click", saveHandler);
    handlers.push({ element: saveBtn, event: "click", handler: saveHandler });

    // Reset all
    const resetBtn = document.getElementById("resetAllBtn");
    const resetHandler = () => {
      this.confirmAction("Reset all settings to defaults?", () => {
        this.resetAllSettings();
      });
    };
    resetBtn.addEventListener("click", resetHandler);
    handlers.push({ element: resetBtn, event: "click", handler: resetHandler });

    return handlers;
  }

  /**
   * Set up range input value displays
   */
  setupRangeInputs() {
    // Define range inputs with their value displays
    const ranges = [];

    const handlers = [];

    ranges.forEach((range) => {
      const input = document.getElementById(range.input);
      const display = document.getElementById(range.display);

      if (!input || !display) return;

      const handler = () => {
        display.textContent = `${input.value}%`;
      };

      input.addEventListener("input", handler);
      handlers.push({ element: input, event: "input", handler });
    });

    return handlers;
  }

  /**
   * Set up form change listeners
   */
  setupFormChangeListeners() {
    // Note: Auto-save has been disabled. Settings will only be saved when
    // the "Save All Settings" button is clicked.

    // Optional: Add visual indicators for unsaved changes
    const formElements = document.querySelectorAll(
      '.form-control, input[type="checkbox"], input[type="color"]'
    );

    formElements.forEach((element) => {
      const eventType = element.type === "range" ? "input" : "change";
      element.addEventListener(eventType, () => {
        // Mark form as having unsaved changes
        this.markFormAsChanged();
      });
    });
  }

  /**
   * Mark form as having unsaved changes
   */
  markFormAsChanged() {
    // Add visual indicator that there are unsaved changes
    const saveBtn = document.getElementById("saveSettingsBtn");
    if (saveBtn && !saveBtn.classList.contains("has-changes")) {
      saveBtn.classList.add("has-changes");
      saveBtn.setAttribute(
        "data-tooltip",
        "Save all current settings permanently (You have unsaved changes)"
      );
    }
  }

  /**
   * Mark form as saved/clean
   */
  markFormAsSaved() {
    const saveBtn = document.getElementById("saveSettingsBtn");
    if (saveBtn) {
      saveBtn.classList.remove("has-changes");
      saveBtn.setAttribute(
        "data-tooltip",
        "Save all current settings permanently"
      );
    }
  }

  /**
   * Set up preset event listeners
   */
  setupPresetEventListeners() {
    document.getElementById("createPresetBtn").addEventListener("click", () => {
      this.createPreset();
    });
  }

  /**
   * Set up backup and restore listeners
   */
  setupBackupRestoreListeners() {
    document
      .getElementById("exportSettingsBtn")
      .addEventListener("click", () => {
        this.exportSettings();
      });

    document
      .getElementById("importSettingsBtn")
      .addEventListener("click", () => {
        this.importSettings();
      });

    document
      .getElementById("resetToDefaultsBtn")
      .addEventListener("click", () => {
        this.confirmAction(
          "Reset all settings to defaults? This cannot be undone.",
          () => {
            this.resetAllSettings();
          }
        );
      });
  }

  /**
   * Set up modal functionality
   */
  setupModal() {
    const modal = document.getElementById("confirmModal");
    const cancelBtn = document.getElementById("modalCancelBtn");
    const confirmBtn = document.getElementById("modalConfirmBtn");

    cancelBtn.addEventListener("click", () => {
      modal.classList.remove("show");
      modal.setAttribute("aria-hidden", "true");
    });

    // Close on escape
    document.addEventListener("keydown", (e) => {
      if (e.key === "Escape" && modal.classList.contains("show")) {
        modal.classList.remove("show");
        modal.setAttribute("aria-hidden", "true");
      }
    });
  }

  /**
   * Initialize UI elements
   */
  initializeUI() {
    // Set initial section
    this.showSection("general");

    // Initialize range values
    // JPEG and WebP quality are now fixed at 100%
  }
  /**
   * Show specific settings section
   */
  showSection(sectionName) {
    // Prevent rapid section switching
    if (this._switching) return;
    this._switching = true;

    // Update navigation
    document.querySelectorAll(".nav-link").forEach((link) => {
      link.classList.remove("active");
    });
    const navLink = document.querySelector(`[data-section="${sectionName}"]`);
    if (navLink) {
      navLink.classList.add("active");
    }

    // Update content with proper animation handling
    document.querySelectorAll(".settings-section").forEach((section) => {
      section.classList.remove("active");
    });

    // Small delay to ensure previous section is hidden before showing new one
    setTimeout(() => {
      const targetSection = document.querySelector(
        `.settings-section[data-section="${sectionName}"], #${sectionName}`
      );
      if (targetSection) {
        targetSection.classList.add("active");
      }
      // Allow next section switch after animation starts
      setTimeout(() => {
        this._switching = false;
      }, 100);
    }, 10);

    this.currentSection = sectionName;

    // Announce section change
    announceToScreenReader(`Switched to ${sectionName} settings`);
  }

  /**
   * Populate form with current settings
   */
  populateForm() {
    console.log("Populating form with settings:", this.settings);
    let foundElements = 0;
    let missingElements = 0;

    Object.entries(this.settings).forEach(([key, value]) => {
      const element = document.getElementById(key);
      if (!element) {
        console.log(`Element not found for setting: ${key}`);
        missingElements++;
        return;
      }

      foundElements++;
      console.log(`Setting ${key} = ${value} (element type: ${element.type})`);

      if (element.type === "checkbox") {
        element.checked = value;
      } else if (element.type === "range") {
        element.value = value;
        // Update display
        const valueDisplay = document.getElementById(`${key}Value`);
        if (valueDisplay) {
          valueDisplay.textContent = `${value}%`;
        }
      } else {
        element.value = value;
      }
    });

    console.log(
      `Form population complete: ${foundElements} elements found, ${missingElements} missing`
    );
  }

  /**
   * Save all settings
   */
  async saveAllSettings() {
    try {
      this.isLoading = true;
      this.setButtonLoading("saveSettingsBtn", true);

      const formData = this.getFormData();
      this.settings = { ...this.settings, ...formData };

      await StorageService.set({ settings: this.settings }, "sync");

      this.showStatus("Settings saved successfully!", "success");
      announceToScreenReader("All settings saved successfully");

      // Mark form as saved (remove unsaved changes indicator)
      this.markFormAsSaved();
    } catch (error) {
      console.error(`${EXTENSION_CONFIG.logPrefix} Save error:`, error);
      this.showStatus("Failed to save settings", "error");
    } finally {
      this.isLoading = false;
      this.setButtonLoading("saveSettingsBtn", false);
    }
  }

  /**
   * Get form data as object with proper type conversion
   */
  getFormData() {
    const formData = {};
    const formElements = document.querySelectorAll(
      '.form-control, input[type="checkbox"], input[type="color"]'
    );

    formElements.forEach((element) => {
      if (!element.id) return;

      if (element.type === "checkbox") {
        formData[element.id] = element.checked;
      } else if (element.type === "number" || element.type === "range") {
        formData[element.id] = parseInt(element.value, 10);
      } else {
        formData[element.id] = element.value;
      }
    });

    return formData;
  }

  /**
   * Log debug message if debug is enabled
   */
  logDebug(message) {
    if (DEBUG.enabled) {
      console.log(`${EXTENSION_CONFIG.logPrefix} ${message}`);
    }
  }

  /**
   * Handle errors consistently
   */
  handleError(message, error) {
    console.error(`${EXTENSION_CONFIG.logPrefix} ${message}`, error);
  }

  /**
   * Load and display presets
   */
  async loadPresets() {
    try {
      const presets = await StorageService.getPresets();
      this.displayPresets(presets);
    } catch (error) {
      console.error(
        `${EXTENSION_CONFIG.logPrefix} Error loading presets:`,
        error
      );
    }
  }

  /**
   * Display presets in the UI
   */
  displayPresets(presets) {
    const container = document.getElementById("presetsList");
    container.innerHTML = "";

    if (Object.keys(presets).length === 0) {
      container.innerHTML =
        '<p class="setting-description">No presets saved yet.</p>';
      return;
    }

    Object.entries(presets).forEach(([name, preset]) => {
      const presetElement = document.createElement("div");
      presetElement.className = "preset-item";
      presetElement.innerHTML = `
        <div class="preset-info">
          <div class="preset-name">${name}</div>
          <div class="preset-details">
            ${preset.format?.toUpperCase() || "PNG"} • 
            ${preset.quality}x • 
            ${preset.transparentBg ? "Transparent" : "Solid background"}
          </div>
        </div>
        <div class="preset-actions">
          <button class="btn btn-danger" data-preset-name="${name}">Delete</button>
        </div>
      `;

      // Add event listener for the delete button
      const deleteButton = presetElement.querySelector(".btn-danger");
      deleteButton.addEventListener("click", () => {
        this.deletePreset(name);
      });

      container.appendChild(presetElement);
    });
  }

  /**
   * Create new preset from form
   */
  async createPreset() {
    const nameInput = document.getElementById("newPresetName");
    const name = nameInput.value.trim();

    if (!name) {
      this.showStatus("Please enter a preset name", "error");
      return;
    }

    const presetData = {
      quality: document.getElementById("presetQuality").value,
      format: document.getElementById("presetFormat").value,
      bgColor: document.getElementById("presetBackground").value,
      transparentBg: document.getElementById("presetTransparent").checked,
    };

    try {
      await StorageService.savePreset(name, presetData);
      nameInput.value = "";
      this.loadPresets();
      this.showStatus(`Preset "${name}" created!`, "success");
    } catch (error) {
      console.error(
        `${EXTENSION_CONFIG.logPrefix} Preset creation error:`,
        error
      );
      this.showStatus("Failed to create preset", "error");
    }
  }

  /**
   * Delete preset
   */
  async deletePreset(name) {
    console.log(`Starting deletion of preset: ${name}`);
    this.confirmAction(`Delete preset "${name}"?`, async () => {
      console.log(`Confirmed deletion of preset: ${name}`);
      try {
        await StorageService.deletePreset(name);
        console.log(`Successfully deleted preset: ${name}`);
        await this.loadPresets();
        this.showStatus(`Preset "${name}" deleted`, "success");
      } catch (error) {
        console.error(
          `${EXTENSION_CONFIG.logPrefix} Preset deletion error:`,
          error
        );
        this.showStatus("Failed to delete preset", "error");
      }
    });
  }

  /**
   * Export all settings to file
   */
  async exportSettings() {
    try {
      const allData = {
        settings: this.settings,
        presets: await StorageService.getPresets(),
        exportDate: new Date().toISOString(),
        version: EXTENSION_CONFIG.version,
      };

      const blob = new Blob([JSON.stringify(allData, null, 2)], {
        type: "application/json",
      });
      const url = URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `svg-switcher-settings-${
        new Date().toISOString().split("T")[0]
      }.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      this.showStatus("Settings exported successfully!", "success");
    } catch (error) {
      console.error(`${EXTENSION_CONFIG.logPrefix} Export error:`, error);
      this.showStatus("Failed to export settings", "error");
    }
  }

  /**
   * Import settings from file
   */
  async importSettings() {
    const fileInput = document.getElementById("importSettingsFile");
    const file = fileInput.files[0];

    if (!file) {
      this.showStatus("Please select a file to import", "error");
      return;
    }

    try {
      const text = await file.text();
      const data = JSON.parse(text);

      // Validate data structure
      if (!data.settings) {
        throw new Error("Invalid settings file format");
      }

      // Import settings
      this.settings = { ...DEFAULT_SETTINGS, ...data.settings };
      await StorageService.set({ settings: this.settings }, "sync");

      // Import presets if available
      if (data.presets) {
        for (const [name, preset] of Object.entries(data.presets)) {
          await StorageService.savePreset(name, preset);
        }
      }

      // Update UI
      this.populateForm();
      this.loadPresets();

      // Mark form as saved since we just imported and saved settings
      this.markFormAsSaved();

      this.showStatus("Settings imported successfully!", "success");
      fileInput.value = "";
    } catch (error) {
      console.error(`${EXTENSION_CONFIG.logPrefix} Import error:`, error);
      this.showStatus(
        "Failed to import settings. Please check the file format.",
        "error"
      );
    }
  }

  /**
   * Reset all settings to defaults
   */
  async resetAllSettings() {
    try {
      console.log("Resetting settings to defaults...");
      const defaultSettings = this.getDefaultSettings();
      console.log("Default settings:", defaultSettings);

      this.settings = defaultSettings;
      await StorageService.set({ settings: this.settings }, "sync");
      console.log("Settings saved to storage");

      this.populateForm();
      console.log("Form populated with default values");

      // Mark form as saved since we just reset and saved the defaults
      this.markFormAsSaved();

      this.showStatus("All settings reset to defaults", "success");
      announceToScreenReader("All settings have been reset to default values");
    } catch (error) {
      console.error(`${EXTENSION_CONFIG.logPrefix} Reset error:`, error);
      this.showStatus("Failed to reset settings", "error");
    }
  }

  /**
   * Show confirmation modal with improved accessibility
   */
  confirmAction(message, callback) {
    const modal = document.getElementById("confirmModal");
    const messageEl = document.getElementById("modalMessage");
    const confirmBtn = document.getElementById("modalConfirmBtn");
    const cancelBtn = document.getElementById("modalCancelBtn");

    messageEl.textContent = message;

    // Remove old listeners safely by cloning buttons
    const newConfirmBtn = confirmBtn.cloneNode(true);
    const newCancelBtn = cancelBtn.cloneNode(true);
    confirmBtn.parentNode.replaceChild(newConfirmBtn, confirmBtn);
    cancelBtn.parentNode.replaceChild(newCancelBtn, cancelBtn);

    // Add confirm listener
    newConfirmBtn.addEventListener("click", async () => {
      this.hideModal(modal);
      try {
        await callback();
      } catch (error) {
        console.error("Callback error:", error);
        this.showStatus("Action failed", "error");
      }
    });

    // Add cancel listener
    newCancelBtn.addEventListener("click", () => {
      this.hideModal(modal);
    });

    this.showModal(modal);
    newConfirmBtn.focus();
  }

  /**
   * Show modal with proper accessibility attributes
   */
  showModal(modal) {
    modal.classList.add("show");
    modal.setAttribute("aria-hidden", "false");
  }

  /**
   * Hide modal with proper accessibility cleanup
   */
  hideModal(modal) {
    modal.classList.remove("show");
    modal.setAttribute("aria-hidden", "true");
  }

  /**
   * Set up floating action button
   */
  setupFloatingActionButton() {
    const fab = document.getElementById("quickSaveFab");
    if (!fab) return;

    const fabHandler = () => {
      this.saveAllSettings();
      // Add visual feedback
      fab.style.transform = "scale(0.9)";
      setTimeout(() => {
        fab.style.transform = "scale(1)";
      }, 150);
    };

    fab.addEventListener("click", fabHandler);
    return [{ element: fab, event: "click", handler: fabHandler }];
  }

  /**
   * Add loading state to buttons (enhanced version)
   */
  setButtonLoading(buttonId, isLoading) {
    const button = document.getElementById(buttonId);
    if (!button) return;

    if (isLoading) {
      button.disabled = true;
      button.style.position = "relative";

      // Create loading overlay if it doesn't exist
      if (!button.querySelector(".loading-overlay")) {
        const loadingOverlay = document.createElement("div");
        loadingOverlay.className = "loading-overlay";
        loadingOverlay.innerHTML = '<div class="loading-spinner"></div>';
        button.appendChild(loadingOverlay);
      }
    } else {
      button.disabled = false;
      const loadingOverlay = button.querySelector(".loading-overlay");
      if (loadingOverlay) {
        loadingOverlay.remove();
      }
    }
  }

  /**
   * Enhanced status message with better styling
   */
  showStatus(message, type = "info", duration = 3000) {
    const statusElement = document.getElementById("statusMessage");
    if (!statusElement) return;

    // Clear any existing timeout
    if (this.statusTimeout) {
      clearTimeout(this.statusTimeout);
    }

    // Set the message and type
    statusElement.textContent = message;
    statusElement.className = `status-message ${type} show`;

    // Auto-hide after duration
    this.statusTimeout = setTimeout(() => {
      statusElement.classList.remove("show");
    }, duration);
  }

  /**
   * Cleanup resources on page unload
   */
  cleanup() {
    // Remove all registered event listeners
    Object.values(this.eventHandlers)
      .flat()
      .forEach((handler) => {
        if (handler && handler.element && handler.event) {
          handler.element.removeEventListener(handler.event, handler.handler);
        }
      });

    // Clear timeouts
    if (this.autoSaveTimeout) {
      clearTimeout(this.autoSaveTimeout);
    }

    if (this.statusTimeout) {
      clearTimeout(this.statusTimeout);
    }

    this.logDebug("Settings controller cleaned up");
  }
}

// Initialize settings controller using modern patterns
document.addEventListener("DOMContentLoaded", async () => {
  try {
    const settingsController = new SettingsController();
    await settingsController.init();

    // Initialize header enhancer with performance checks
    if (
      window.innerWidth > 768 &&
      !window.matchMedia("(prefers-reduced-motion: reduce)").matches
    ) {
      const { HeaderEnhancer } = await import(
        "../src/settings/header-enhancer.js"
      );
      new HeaderEnhancer().init();
    }

    // Make controller available globally for onclick handlers
    window.settingsController = settingsController;

    // Cleanup on page unload
    window.addEventListener("beforeunload", () => {
      settingsController.cleanup();
    });
  } catch (error) {
    console.error("Failed to initialize settings:", error);
  }
});
