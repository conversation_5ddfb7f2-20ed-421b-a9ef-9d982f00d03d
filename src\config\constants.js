/**
 * SVG Switcher Extension Configuration Constants
 * Centralizes all configuration values for better maintainability
 */

// Extension Information
export const EXTENSION_CONFIG = {
  name: "SVG Switcher",
  version: "2.0.0",
  logPrefix: "SVG Switcher:",
};

// Comprehensive Default Settings - Single Source of Truth
export const DEFAULT_SETTINGS = {
  // Basic settings (backward compatibility)
  quality: "4",
  format: "png",
  bgColor: "#ffffff",
  transparentBg: false,
  filename: "image",
  downloadType: "separate",

  // General settings
  defaultFilename: "image",
  filenameCase: "original",
  replaceSpaces: false,
  appendTimestamp: false,
  defaultQuality: "4",
  maxDimension: 8192,

  // Export settings
  defaultFormat: "png",
  defaultTransparent: false,
  defaultBackgroundColor: "#ffffff",
  preserveAspectRatio: true,

  // Behavior settings
  showToolbarOnSelection: true,
  autoHideToolbar: false,
  toolbarPosition: "top-right",
  rememberToolbarPosition: false,
  selectionModifier: "ctrl",
  clearOnEscape: true,
  visualFeedback: true,
  defaultDownloadType: "separate",
  showPreview: true,
  confirmBeforeDownload: false,

  // Advanced settings
  maxConcurrentConversions: 3,
  conversionTimeout: 30,
  enableCaching: true,
  cacheSize: 50,
  enableDebugMode: false,
  showPerformanceMetrics: false,

  // Accessibility settings
  enableScreenReaderSupport: true,
  highContrastMode: false,
  reduceMotion: false,
};

// File Format Configuration
export const SUPPORTED_FORMATS = {
  PNG: {
    value: "png",
    label: "PNG",
    mimeType: "image/png",
    extension: ".png",
  },
  JPEG: {
    value: "jpeg",
    label: "JPEG",
    mimeType: "image/jpeg",
    extension: ".jpg",
  },
  WEBP: {
    value: "webp",
    label: "WebP",
    mimeType: "image/webp",
    extension: ".webp",
  },
  SVG: {
    value: "svg",
    label: "SVG (Original)",
    mimeType: "image/svg+xml",
    extension: ".svg",
  },
};

// Quality Scale Options
export const QUALITY_OPTIONS = [
  { value: "1", label: "Original Size (1x)" },
  { value: "2", label: "High Quality (2x)" },
  { value: "3", label: "Ultra Quality (3x)" },
  { value: "4", label: "Maximum Quality (4x)" },
];

// CSS Class Names
export const CSS_CLASSES = {
  selected: "svg2png-selected",
  toolbar: "svg2png-toolbar",
  toolbarHeader: "svg2png-toolbar-header",
  toolbarBadge: "svg2png-toolbar-badge",
  toolbarClose: "svg2png-toolbar-close",
  toolbarContent: "svg2png-toolbar-content",
  toolbarBtn: "svg2png-toolbar-btn",
  closing: "closing",
};

// Element IDs
export const ELEMENT_IDS = {
  // Popup Elements
  status: "status",
  svgInfo: "svgInfo",
  svgDetails: "svgDetails",
  downloadBtn: "downloadBtn",
  copyToClipboardBtn: "copyToClipboardBtn",
  filename: "filename",
  quality: "quality",
  customWidth: "customWidth",
  customHeight: "customHeight",
  bgColor: "bgColor",
  transparentBg: "transparentBg",
  format: "format",
  batchControls: "batchControls",
  progressBar: "progressBar",
  progressBarFill: "progressBarFill",
  previewContainer: "previewContainer",
  previewCanvas: "previewCanvas",
  presetSelect: "presetSelect",
  presetName: "presetName",
  savePresetBtn: "savePresetBtn",
  deletePresetBtn: "deletePresetBtn",
  // CSS Style Element
  styles: "svg-switcher-styles",
};

// Message Actions
export const MESSAGE_ACTIONS = {
  clearSelections: "clearSelections",
  getSelectionCount: "getSelectionCount",
  getSVGData: "getSVGData",
  debugSelections: "debugSelections",
  downloadFromToolbar: "downloadFromToolbar",
};

// Storage Keys
export const STORAGE_KEYS = {
  presets: "presets",
};

// Animation Durations (in milliseconds)
export const ANIMATION = {
  slideOut: 200,
  progressDelay: 1000,
};

// UI Configuration
export const UI_CONFIG = {
  popup: {
    width: 300,
    maxFilenamePlaceholder: 50,
  },
  toolbar: {
    minWidth: 200,
    defaultPosition: { top: 20, right: 20 },
  },
  canvas: {
    maxPreviewWidth: 280,
    maxPreviewHeight: 200,
  },
};

// Validation Rules
export const VALIDATION = {
  filename: {
    minLength: 1,
    maxLength: 255,
    invalidChars: /[<>:"/\\|?*]/g,
  },
  dimensions: {
    min: 1,
    max: 8192,
  },
  quality: {
    min: 1,
    max: 4,
  },
};

// Error Messages
export const ERROR_MESSAGES = {
  noSelection: "No SVG selected",
  invalidFilename: "Please enter a valid filename",
  conversionFailed: "Failed to convert SVG",
  downloadFailed: "Failed to download file",
  clipboardFailed: "Failed to copy to clipboard",
  storageError: "Failed to access storage",
  presetExists: "Preset name already exists",
  presetNameRequired: "Please enter a preset name",
};

// Success Messages
export const SUCCESS_MESSAGES = {
  presetSaved: "Preset saved successfully",
  presetDeleted: "Preset deleted successfully",
  selectionCleared: "All selections cleared",
  clipboardCopy: "Copied to clipboard successfully",
};

// Debug Configuration - Environment-based (false for production builds)
export const DEBUG = {
  enabled: false, // Set to true during development
  logLevel: "info", // 'debug', 'info', 'warn', 'error'
};
