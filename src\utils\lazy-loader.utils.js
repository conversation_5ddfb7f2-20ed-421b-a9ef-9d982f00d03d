/**
 * Lazy Loading Utilities
 * Provides efficient module and resource loading capabilities
 */

import { EXTENSION_CONFIG, DEBUG } from "../config/constants.js";
import { logDebug, logError, logWarn } from "./logger.utils.js";

/**
 * Lazy loader singleton for module caching and loading optimization
 */
class LazyLoader {
  constructor() {
    this.moduleCache = new Map();
    this.loadingPromises = new Map();
    this.criticalModules = new Set();
    this.initialized = false;
  }

  /**
   * Initialize the lazy loader
   */
  initialize() {
    if (this.initialized) return;

    // Define critical modules that should be preloaded
    this.criticalModules = new Set([
      "src/config/constants.js",
      "src/utils/dom.utils.js",
      "src/utils/error-handler.utils.js",
      "src/services/storage.service.js",
    ]);

    this.initialized = true;

    logDebug("Lazy loader initialized", "LazyLoader");
  }

  /**
   * Load module with caching
   * @param {string} modulePath - Path to module
   * @returns {Promise<any>} Loaded module
   */
  async loadModule(modulePath) {
    // Check cache first
    if (this.moduleCache.has(modulePath)) {
      logDebug(`Loading from cache: ${modulePath}`, "LazyLoader");
      return this.moduleCache.get(modulePath);
    }

    // Check if already loading
    if (this.loadingPromises.has(modulePath)) {
      return this.loadingPromises.get(modulePath);
    }

    // Create loading promise
    const loadingPromise = this._loadModuleInternal(modulePath);
    this.loadingPromises.set(modulePath, loadingPromise);

    try {
      const module = await loadingPromise;
      this.moduleCache.set(modulePath, module);
      this.loadingPromises.delete(modulePath);

      logDebug(`Module loaded: ${modulePath}`, "LazyLoader");

      return module;
    } catch (error) {
      this.loadingPromises.delete(modulePath);
      logError(`Failed to load module: ${modulePath}`, "LazyLoader", error);
      throw error;
    }
  }

  /**
   * Internal module loading
   * @param {string} modulePath - Path to module
   * @returns {Promise<any>} Loaded module
   * @private
   */
  async _loadModuleInternal(modulePath) {
    const fullPath = chrome.runtime.getURL(modulePath);
    return import(fullPath);
  }

  /**
   * Preload critical modules
   * @returns {Promise<void>}
   */
  async preloadCriticalModules() {
    const loadPromises = Array.from(this.criticalModules).map(
      async (modulePath) => {
        try {
          await this.loadModule(modulePath);
        } catch (error) {
          logError(
            `Failed to preload critical module: ${modulePath}`,
            "LazyLoader",
            error
          );
        }
      }
    );

    await Promise.allSettled(loadPromises);

    logDebug("Critical modules preloaded", "LazyLoader");
  }

  /**
   * Load component lazily with intersection observer
   * @param {Element} element - Element to observe
   * @param {string} componentPath - Path to component module
   * @param {Object} options - Intersection observer options
   * @returns {Promise<void>}
   */
  async loadComponentOnIntersection(element, componentPath, options = {}) {
    const defaultOptions = {
      root: null,
      rootMargin: "50px",
      threshold: 0.1,
      ...options,
    };

    return new Promise((resolve, reject) => {
      const observer = new IntersectionObserver(async (entries) => {
        for (const entry of entries) {
          if (entry.isIntersecting) {
            observer.disconnect();
            try {
              const component = await this.loadModule(componentPath);
              resolve(component);
            } catch (error) {
              reject(error);
            }
            break;
          }
        }
      }, defaultOptions);

      observer.observe(element);
    });
  }

  /**
   * Load resources with retry logic
   * @param {Array<string>} resourcePaths - Array of resource paths
   * @param {number} maxRetries - Maximum retry attempts
   * @returns {Promise<Array>}
   */
  async loadResourcesWithRetry(resourcePaths, maxRetries = 3) {
    const results = [];

    for (const resourcePath of resourcePaths) {
      let lastError;
      let attempts = 0;

      while (attempts < maxRetries) {
        try {
          const resource = await this.loadModule(resourcePath);
          results.push({ path: resourcePath, resource, success: true });
          break;
        } catch (error) {
          lastError = error;
          attempts++;

          if (attempts < maxRetries) {
            // Exponential backoff
            const delay = Math.pow(2, attempts - 1) * 1000;
            await new Promise((resolve) => setTimeout(resolve, delay));
          }
        }
      }

      if (attempts === maxRetries) {
        results.push({ path: resourcePath, error: lastError, success: false });
      }
    }

    return results;
  }

  /**
   * Prefetch module for future use
   * @param {string} modulePath - Path to module to prefetch
   */
  prefetchModule(modulePath) {
    // Use requestIdleCallback if available, otherwise setTimeout
    const scheduleLoad = (callback) => {
      if (window.requestIdleCallback) {
        window.requestIdleCallback(callback, { timeout: 2000 });
      } else {
        setTimeout(callback, 100);
      }
    };

    scheduleLoad(() => {
      this.loadModule(modulePath).catch((error) => {
        logDebug(`Prefetch failed for: ${modulePath}`, "LazyLoader", error);
      });
    });
  }

  /**
   * Clear module cache
   * @param {string} modulePath - Specific module to clear, or all if not provided
   */
  clearCache(modulePath) {
    if (modulePath) {
      this.moduleCache.delete(modulePath);
      this.loadingPromises.delete(modulePath);
    } else {
      this.moduleCache.clear();
      this.loadingPromises.clear();
    }

    const target = modulePath || "all modules";
    logDebug(`Cache cleared for: ${target}`, "LazyLoader");
  }

  /**
   * Get cache statistics
   * @returns {Object} Cache statistics
   */
  getCacheStats() {
    return {
      cachedModules: this.moduleCache.size,
      loadingModules: this.loadingPromises.size,
      criticalModules: this.criticalModules.size,
      cacheHitRate: this._calculateCacheHitRate(),
    };
  }

  /**
   * Calculate cache hit rate (simplified)
   * @returns {number} Cache hit rate percentage
   * @private
   */
  _calculateCacheHitRate() {
    // This is a simplified calculation
    // In a real implementation, you'd track hits vs misses
    const totalRequests = this.moduleCache.size + this.loadingPromises.size;
    if (totalRequests === 0) return 0;
    return Math.round((this.moduleCache.size / totalRequests) * 100);
  }

  /**
   * Clean up lazy loader
   */
  cleanup() {
    this.moduleCache.clear();
    this.loadingPromises.clear();
    this.criticalModules.clear();
    this.initialized = false;
  }
}

// Create singleton instance
const lazyLoader = new LazyLoader();

/**
 * Initialize lazy loader
 */
export function initializeLazyLoader() {
  lazyLoader.initialize();
}

/**
 * Load module with lazy loading
 * @param {string} modulePath - Path to module
 * @returns {Promise<any>} Loaded module
 */
export function loadModule(modulePath) {
  return lazyLoader.loadModule(modulePath);
}

/**
 * Preload critical modules
 * @returns {Promise<void>}
 */
export function preloadCriticalModules() {
  return lazyLoader.preloadCriticalModules();
}

/**
 * Load component on intersection
 * @param {Element} element - Element to observe
 * @param {string} componentPath - Component path
 * @param {Object} options - Observer options
 * @returns {Promise<any>}
 */
export function loadComponentOnIntersection(element, componentPath, options) {
  return lazyLoader.loadComponentOnIntersection(
    element,
    componentPath,
    options
  );
}

/**
 * Prefetch module
 * @param {string} modulePath - Module path
 */
export function prefetchModule(modulePath) {
  lazyLoader.prefetchModule(modulePath);
}

/**
 * Clear module cache
 * @param {string} modulePath - Optional specific module path
 */
export function clearModuleCache(modulePath) {
  lazyLoader.clearCache(modulePath);
}

/**
 * Get cache statistics
 * @returns {Object} Cache stats
 */
export function getCacheStats() {
  return lazyLoader.getCacheStats();
}

/**
 * Cleanup lazy loader
 */
export function cleanupLazyLoader() {
  lazyLoader.cleanup();
}
