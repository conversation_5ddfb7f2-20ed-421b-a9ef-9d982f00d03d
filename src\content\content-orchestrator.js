/**
 * Content Script Orchestrator
 * Main controller that coordinates all content script modules
 */

import { StateManager } from "./state-manager.js";
import { StyleManager } from "./style-manager.js";
import { ToolbarManager } from "./toolbar-manager.js";
import { <PERSON>Handler } from "./event-handler.js";
import { MessageHandler } from "./message-handler.js";
import * as SVGUtils from "../utils/svg.utils.js";
import { EXTENSION_CONFIG } from "../config/constants.js";
import { logDebug, logError } from "../utils/logger.utils.js";

/**
 * Main orchestrator for content script functionality
 */
export class ContentOrchestrator {
  constructor() {
    this.isInitialized = false;
    this.modules = {};
    this.cleanupTasks = [];
  }

  /**
   * Initialize all content script modules
   */
  async init() {
    if (this.isInitialized) {
      logDebug("Content script already initialized");
      return;
    }

    try {
      logDebug("Initializing content script modules...");

      await this._initializeModules();
      this._setupCleanupHandlers();

      this.isInitialized = true;
      logDebug("Content script initialized successfully");
    } catch (error) {
      logError(`Content script initialization error: ${error.message}`);
      this.cleanup();
      throw error;
    }
  }

  /**
   * Initialize all required modules
   * @private
   */
  async _initializeModules() {
    // Initialize core modules
    this.modules.stateManager = new StateManager();
    this.modules.styleManager = new StyleManager();
    this.modules.svgUtils = SVGUtils;

    // Initialize UI modules
    this.modules.toolbarManager = new ToolbarManager(
      this.modules.stateManager,
      this.modules.styleManager
    );

    this.modules.eventHandler = new EventHandler(
      this.modules.stateManager,
      this.modules.toolbarManager,
      this.modules.svgUtils
    );

    // Initialize communication module
    this.modules.messageHandler = new MessageHandler(
      this.modules.stateManager,
      this.modules.svgUtils
    );

    // Start services
    this.modules.styleManager.injectStyles();
    this.modules.eventHandler.attachEventListeners();
    this.modules.messageHandler.startListening();

    // Mark state as initialized
    this.modules.stateManager.setInitialized();

    logDebug("Content script initialized", "", {
      stylesInjected: this.modules.styleManager.areStylesInjected(),
      listenersAttached: this.modules.eventHandler.isListenersAttached(),
      messageHandlerListening:
        this.modules.messageHandler.isCurrentlyListening(),
      modules: Object.keys(this.modules),
    });
  }

  /**
   * Add event listener with automatic cleanup registration
   * @param {EventTarget} target - Event target (e.g., window, document)
   * @param {string} type - Event type (e.g., "click", "beforeunload")
   * @param {Function} listener - Event listener function
   * @param {Object} options - Event listener options
   * @private
   */
  _addManagedEventListener(target, type, listener, options = {}) {
    target.addEventListener(type, listener, options);
    this.cleanupTasks.push(() => {
      target.removeEventListener(type, listener, options);
    });
  }

  /**
   * Set up cleanup handlers for proper resource management
   * @private
   */
  _setupCleanupHandlers() {
    // Handle page unload
    this._addManagedEventListener(window, "beforeunload", () => this.cleanup());

    // Handle extension context invalidation
    const contextHandler = () => {
      if (chrome.runtime?.id) return; // Context still valid
      this.cleanup();
    };

    // Check context periodically
    const contextInterval = setInterval(contextHandler, 5000);
    this.cleanupTasks.push(() => {
      clearInterval(contextInterval);
    });

    // Handle page visibility changes
    this._addManagedEventListener(document, "visibilitychange", () => {
      if (document.hidden) {
        this._pauseOperations();
      } else {
        this._resumeOperations();
      }
    });
  }

  /**
   * Pause non-essential operations when page is hidden
   * @private
   */
  _pauseOperations() {
    logDebug("Pausing operations (page hidden)");

    // Hide toolbar but keep selections
    if (this.modules.toolbarManager) {
      this.modules.toolbarManager.hideToolbar();
    }
  }

  /**
   * Resume operations when page becomes visible
   * @private
   */
  _resumeOperations() {
    logDebug("Resuming operations (page visible)");

    // Restore toolbar if there are selections
    if (this.modules.stateManager?.getSelectionCount() > 0) {
      this.modules.toolbarManager?.createToolbar().catch(console.error);
    }
  }

  /**
   * Get a specific module instance
   * @param {string} moduleName - Name of the module
   * @returns {Object|null} Module instance or null
   */
  getModule(moduleName) {
    return this.modules[moduleName] || null;
  }

  /**
   * Check if orchestrator is initialized
   * @returns {boolean} Whether orchestrator is initialized
   */
  isContentInitialized() {
    return this.isInitialized;
  }

  /**
   * Get current state summary
   * @returns {Object} State summary
   */
  getStateSummary() {
    if (!this.isInitialized) {
      return { initialized: false };
    }

    return {
      initialized: true,
      selectionCount: this.modules.stateManager?.getSelectionCount() || 0,
      toolbarVisible: this.modules.toolbarManager?.isVisible() || false,
      listenersAttached:
        this.modules.eventHandler?.isListenersAttached() || false,
      messageHandlerActive:
        this.modules.messageHandler?.isCurrentlyListening() || false,
      stylesInjected: this.modules.styleManager?.areStylesInjected() || false,
    };
  }

  /**
   * Reinitialize if needed (useful for SPA navigation)
   */
  async reinitialize() {
    logDebug("Reinitializing content script...");
    this.cleanup();
    await this.init();
  }

  /**
   * Clean up all modules and resources
   */
  cleanup() {
    if (!this.isInitialized) return;

    try {
      logDebug("Cleaning up content script...");

      // Clean up modules in reverse order
      Object.values(this.modules)
        .reverse()
        .forEach((module) => {
          if (module && typeof module.cleanup === "function") {
            try {
              module.cleanup();
            } catch (error) {
              logError(`Module cleanup error: ${error.message}`);
            }
          }
        });

      // Run cleanup tasks
      this.cleanupTasks.forEach((task) => {
        try {
          task();
        } catch (error) {
          logError(`Cleanup task error: ${error.message}`);
        }
      });

      // Reset state
      this.modules = {};
      this.cleanupTasks = [];
      this.isInitialized = false;

      logDebug("Content script cleaned up successfully");
    } catch (error) {
      logError(`Cleanup error: ${error.message}`);
    }
  }
}

// Create and initialize the orchestrator
let orchestrator = null;

/**
 * Get the global orchestrator instance
 * @returns {ContentOrchestrator} Orchestrator instance
 */
export function getOrchestrator() {
  if (!orchestrator) {
    orchestrator = new ContentOrchestrator();
  }
  return orchestrator;
}

/**
 * Initialize content script (called from main content.js)
 */
export async function initializeContentScript() {
  const orch = getOrchestrator();
  await orch.init();
  return orch;
}

/**
 * Cleanup content script (called from main content.js)
 */
export function cleanupContentScript() {
  if (orchestrator) {
    orchestrator.cleanup();
    orchestrator = null;
  }
}
