/**
 * SVG Switcher Extension - Popup Entry Point
 * Simplified using shared initialization utilities
 */

import { PopupController } from "../src/popup/popup-controller.js";
import {
  initializeBase,
  handleInitError,
  setupCleanupHandler,
} from "../src/utils/init.utils.js";
import { DEBUG } from "../src/config/constants.js";
import { logDebug } from "../src/utils/logger.utils.js";

/**
 * Main popup initialization
 */
document.addEventListener("DOMContentLoaded", async () => {
  try {
    // Initialize common base features
    await initializeBase("Popup", {
      preloadModules: true,
      initAccessibility: true,
      setupErrorHandling: true,
    });

    // Initialize the popup controller
    const popupController = new PopupController();
    await popupController.initialize();
    logDebug("PopupController initialized", "Popup");

    // Expose controller for debugging in development
    if (DEBUG.enabled) {
      window.svg2pngPopup = popupController;
    }

    logDebug("Popup initialized successfully", "Popup");
  } catch (error) {
    handleInitError(error, "Popup");
  }
});

/**
 * Handle popup cleanup on window unload
 */
setupCleanupHandler("Popup", () => {
  // Cleanup can be added here if needed
});
