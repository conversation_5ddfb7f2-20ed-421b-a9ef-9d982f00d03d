/**
 * Conversion Service
 * Handles SVG to image conversion operations
 */

import {
  EXTENSION_CONFIG,
  ERROR_MESSAGES,
  SUPPORTED_FORMATS,
  DEBUG,
} from "../config/constants.js";
import {
  processSVGForConversion,
  calculateOutputDimensions,
  createSVGDataURL,
  getMimeType,
} from "../utils/svg.utils.js";
import { normalizeSettings } from "../utils/validation.utils.js";
import { processFilenameWithIndex } from "../utils/filename.utils.js";

/**
 * Conversion service class for handling SVG to image conversions
 */
export class ConversionService {
  /**
   * Convert SVG data to a blob or return original SVG blob
   * @param {Object} svgData - SVG data object
   * @param {Object} settings - Conversion settings
   * @returns {Promise<Blob>} Converted image blob or original SVG blob
   */
  static async convertSVGToBlob(svgData, settings) {
    // If format is SVG, return original SVG as blob without conversion
    if (settings.format === "svg") {
      return this.createSVGBlob(svgData);
    }

    return new Promise((resolve, reject) => {
      try {
        // Normalize and validate settings
        const normalizedSettings = normalizeSettings(settings);

        // Calculate output dimensions
        const dimensions = calculateOutputDimensions(
          svgData,
          normalizedSettings
        );

        // Create canvas for conversion
        const canvas = document.createElement("canvas");
        const ctx = canvas.getContext("2d");

        // Set canvas dimensions
        canvas.width = dimensions.width;
        canvas.height = dimensions.height;

        // Set background if not transparent
        if (!normalizedSettings.transparentBg) {
          ctx.fillStyle = normalizedSettings.bgColor;
          ctx.fillRect(0, 0, dimensions.width, dimensions.height);
        }

        // Process SVG for conversion
        const processedSVG = processSVGForConversion(svgData.svgString, {
          width: svgData.width,
          height: svgData.height,
        });

        // Create SVG data URL
        const svgDataUrl = createSVGDataURL(processedSVG);

        // Create image and load SVG
        const img = new Image();

        img.onload = () => {
          try {
            // Draw image to canvas
            ctx.drawImage(img, 0, 0, dimensions.width, dimensions.height);

            // Convert canvas to blob
            const mimeType = getMimeType(normalizedSettings.format);
            // Always use 100% quality for JPEG and WebP formats
            const quality =
              normalizedSettings.format === "jpeg" ||
              normalizedSettings.format === "webp"
                ? 1.0
                : undefined;

            canvas.toBlob(
              (blob) => {
                if (blob) {
                  if (DEBUG.enabled) {
                    console.log(
                      `${EXTENSION_CONFIG.logPrefix} Converted SVG to ${normalizedSettings.format}:`,
                      `${dimensions.width}x${dimensions.height}px`
                    );
                  }
                  resolve(blob);
                } else {
                  reject(new Error("Failed to create image blob"));
                }
              },
              mimeType,
              quality
            );
          } catch (error) {
            reject(new Error(`Canvas conversion failed: ${error.message}`));
          }
        };

        img.onerror = (error) => {
          reject(new Error(`SVG image load failed: ${error}`));
        };

        // Start loading the SVG
        img.src = svgDataUrl;
      } catch (error) {
        reject(
          new Error(`${ERROR_MESSAGES.conversionFailed}: ${error.message}`)
        );
      }
    });
  }

  /**
   * Create SVG blob from SVG data without conversion
   * @param {Object} svgData - SVG data object
   * @returns {Promise<Blob>} Original SVG as blob
   */
  static async createSVGBlob(svgData) {
    try {
      if (DEBUG.enabled) {
        console.log(
          `${EXTENSION_CONFIG.logPrefix} Creating SVG blob for original download`
        );
      }

      const svgString = svgData.svgString;
      const blob = new Blob([svgString], { type: "image/svg+xml" });

      if (DEBUG.enabled) {
        console.log(
          `${EXTENSION_CONFIG.logPrefix} SVG blob created successfully`
        );
      }

      return blob;
    } catch (error) {
      throw new Error(`Failed to create SVG blob: ${error.message}`);
    }
  }

  /**
   * Convert SVG data and trigger download
   * @param {Object} svgData - SVG data object
   * @param {string} filename - Output filename (without extension)
   * @param {Object} settings - Conversion settings
   * @returns {Promise<void>}
   */
  static async convertAndDownload(svgData, filename, settings) {
    try {
      if (DEBUG.enabled) {
        console.log(
          `${EXTENSION_CONFIG.logPrefix} Converting and downloading:`,
          filename
        );
      }

      // Convert SVG to blob
      const blob = await this.convertSVGToBlob(svgData, settings);

      // Get file extension
      const format = settings.format || "png";
      const formatConfig = Object.values(SUPPORTED_FORMATS).find(
        (f) => f.value === format
      );
      const extension = formatConfig ? formatConfig.extension : ".png";

      // Create download URL
      const url = URL.createObjectURL(blob);

      // Trigger download
      await chrome.downloads.download({
        url: url,
        filename: `${filename}${extension}`,
        saveAs: true,
      });

      // Clean up URL
      URL.revokeObjectURL(url);

      if (DEBUG.enabled) {
        console.log(
          `${EXTENSION_CONFIG.logPrefix} Download triggered for:`,
          filename
        );
      }
    } catch (error) {
      console.error(
        `${EXTENSION_CONFIG.logPrefix} Conversion and download error:`,
        error
      );
      throw new Error(`${ERROR_MESSAGES.downloadFailed}: ${error.message}`);
    }
  }

  /**
   * Convert multiple SVGs to individual files
   * @param {Array} svgDataArray - Array of SVG data objects
   * @param {string} baseFilename - Base filename for all files
   * @param {Object} settings - Conversion settings
   * @param {Function} progressCallback - Progress callback function
   * @param {Object} pageContext - Page context for filename placeholders
   * @returns {Promise<void>}
   */
  static async convertMultipleSeparate(
    svgDataArray,
    baseFilename,
    settings,
    progressCallback,
    pageContext = null
  ) {
    try {
      if (!Array.isArray(svgDataArray) || svgDataArray.length === 0) {
        throw new Error("No SVG data provided");
      }

      if (DEBUG.enabled) {
        console.log(
          `${EXTENSION_CONFIG.logPrefix} Converting ${svgDataArray.length} SVGs separately`
        );
      }

      const total = svgDataArray.length;

      for (let i = 0; i < total; i++) {
        const svgData = svgDataArray[i];
        const filename = processFilenameWithIndex(
          baseFilename,
          i + 1,
          total,
          pageContext
        );

        // Convert and download individual file
        await this.convertAndDownload(svgData, filename, settings);

        // Update progress
        if (progressCallback) {
          const progress = ((i + 1) / total) * 100;
          progressCallback(progress);
        }
      }

      if (DEBUG.enabled) {
        console.log(
          `${EXTENSION_CONFIG.logPrefix} Completed separate conversions`
        );
      }
    } catch (error) {
      console.error(
        `${EXTENSION_CONFIG.logPrefix} Multiple conversion error:`,
        error
      );
      throw error;
    }
  }

  /**
   * Convert multiple SVGs to a ZIP archive
   * @param {Array} svgDataArray - Array of SVG data objects
   * @param {string} baseFilename - Base filename for ZIP and files
   * @param {Object} settings - Conversion settings
   * @param {Function} progressCallback - Progress callback function
   * @param {Object} pageContext - Page context for filename placeholders
   * @returns {Promise<void>}
   */
  static async convertMultipleToZip(
    svgDataArray,
    baseFilename,
    settings,
    progressCallback,
    pageContext = null
  ) {
    try {
      if (!Array.isArray(svgDataArray) || svgDataArray.length === 0) {
        throw new Error("No SVG data provided");
      }

      // Check if JSZip is available
      if (typeof JSZip === "undefined") {
        throw new Error("JSZip library not available");
      }

      if (DEBUG.enabled) {
        console.log(
          `${EXTENSION_CONFIG.logPrefix} Converting ${svgDataArray.length} SVGs to ZIP`
        );
      }

      const zip = new JSZip();
      const total = svgDataArray.length;

      // Get file extension
      const format = settings.format || "png";
      const formatConfig = Object.values(SUPPORTED_FORMATS).find(
        (f) => f.value === format
      );
      const extension = formatConfig ? formatConfig.extension : ".png";

      // Convert each SVG and add to ZIP
      for (let i = 0; i < total; i++) {
        const svgData = svgDataArray[i];

        // For ZIP archives, ensure each file has a unique name
        // If the base filename doesn't contain {index} placeholder, append index
        let filenamePattern = baseFilename;
        if (!baseFilename.includes("{index}") && total > 1) {
          filenamePattern = `${baseFilename}_{index}`;
        }

        const filenameWithoutExt = processFilenameWithIndex(
          filenamePattern,
          i + 1,
          total,
          pageContext
        );
        const filename = `${filenameWithoutExt}${extension}`;

        // Convert SVG to blob
        const blob = await this.convertSVGToBlob(svgData, settings);

        // Add to ZIP
        zip.file(filename, blob);

        // Update progress
        if (progressCallback) {
          const progress = ((i + 1) / total) * 100;
          progressCallback(progress);
        }
      }

      // Generate ZIP blob
      const zipBlob = await zip.generateAsync({ type: "blob" });

      // Create download URL
      const url = URL.createObjectURL(zipBlob);

      // Trigger download
      const zipFilename = processFilenameWithIndex(
        baseFilename,
        1,
        1,
        pageContext
      );
      await chrome.downloads.download({
        url: url,
        filename: `${zipFilename}_svgs.zip`,
        saveAs: true,
      });

      // Clean up URL
      URL.revokeObjectURL(url);

      if (DEBUG.enabled) {
        console.log(`${EXTENSION_CONFIG.logPrefix} ZIP download triggered`);
      }
    } catch (error) {
      console.error(
        `${EXTENSION_CONFIG.logPrefix} ZIP conversion error:`,
        error
      );
      throw new Error(`${ERROR_MESSAGES.downloadFailed}: ${error.message}`);
    }
  }

  /**
   * Generate preview for a single SVG
   * @param {Object} svgData - SVG data object
   * @param {Object} settings - Conversion settings
   * @param {number} maxWidth - Maximum preview width
   * @param {number} maxHeight - Maximum preview height
   * @returns {Promise<HTMLCanvasElement>} Canvas with preview
   */
  static async generatePreview(
    svgData,
    settings,
    maxWidth = 280,
    maxHeight = 200
  ) {
    return new Promise((resolve, reject) => {
      try {
        // Normalize settings
        const normalizedSettings = normalizeSettings(settings);

        // Calculate preview dimensions (maintain aspect ratio)
        const dimensions = calculateOutputDimensions(
          svgData,
          normalizedSettings
        );
        const aspectRatio = dimensions.width / dimensions.height;

        let previewWidth, previewHeight;

        if (dimensions.width > maxWidth || dimensions.height > maxHeight) {
          if (aspectRatio > maxWidth / maxHeight) {
            previewWidth = maxWidth;
            previewHeight = maxWidth / aspectRatio;
          } else {
            previewHeight = maxHeight;
            previewWidth = maxHeight * aspectRatio;
          }
        } else {
          previewWidth = dimensions.width;
          previewHeight = dimensions.height;
        }

        // Create preview canvas
        const canvas = document.createElement("canvas");
        const ctx = canvas.getContext("2d");

        canvas.width = Math.round(previewWidth);
        canvas.height = Math.round(previewHeight);

        // Set background if not transparent
        if (!normalizedSettings.transparentBg) {
          ctx.fillStyle = normalizedSettings.bgColor;
          ctx.fillRect(0, 0, canvas.width, canvas.height);
        }

        // Process SVG
        const processedSVG = processSVGForConversion(svgData.svgString);
        const svgDataUrl = createSVGDataURL(processedSVG);

        // Load and draw SVG
        const img = new Image();

        img.onload = () => {
          ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
          resolve(canvas);
        };

        img.onerror = (error) => {
          reject(new Error(`Preview generation failed: ${error}`));
        };

        img.src = svgDataUrl;
      } catch (error) {
        reject(new Error(`Preview generation error: ${error.message}`));
      }
    });
  }

  /**
   * Copy SVG as image to clipboard
   * @param {Object} svgData - SVG data object
   * @param {Object} settings - Conversion settings
   * @returns {Promise<void>}
   */
  static async copyToClipboard(svgData, settings) {
    try {
      if (DEBUG.enabled) {
        console.log(`${EXTENSION_CONFIG.logPrefix} Copying SVG to clipboard`);
      }

      // If format is SVG, copy as text
      if (settings.format === "svg") {
        await navigator.clipboard.writeText(svgData.svgString);

        if (DEBUG.enabled) {
          console.log(
            `${EXTENSION_CONFIG.logPrefix} Successfully copied SVG text to clipboard`
          );
        }
        return;
      }

      // Convert SVG to blob for other formats
      const blob = await this.convertSVGToBlob(svgData, settings);

      // Use the Clipboard API to write the image
      await navigator.clipboard.write([
        new ClipboardItem({
          [blob.type]: blob,
        }),
      ]);

      if (DEBUG.enabled) {
        console.log(
          `${EXTENSION_CONFIG.logPrefix} Successfully copied ${settings.format} image to clipboard`
        );
      }
    } catch (error) {
      console.error(
        `${EXTENSION_CONFIG.logPrefix} Clipboard copy error:`,
        error
      );
      throw new Error(`Failed to copy to clipboard: ${error.message}`);
    }
  }

  /**
   * Copy multiple SVGs to clipboard (copies the first one)
   * @param {Array} svgDataArray - Array of SVG data objects
   * @param {Object} settings - Conversion settings
   * @returns {Promise<void>}
   */
  static async copyMultipleToClipboard(svgDataArray, settings) {
    try {
      if (!Array.isArray(svgDataArray) || svgDataArray.length === 0) {
        throw new Error("No SVG data provided");
      }

      if (DEBUG.enabled) {
        console.log(
          `${EXTENSION_CONFIG.logPrefix} Copying first of ${svgDataArray.length} SVGs to clipboard`
        );
      }

      // Copy the first SVG to clipboard
      await this.copyToClipboard(svgDataArray[0], settings);
    } catch (error) {
      console.error(
        `${EXTENSION_CONFIG.logPrefix} Multiple clipboard copy error:`,
        error
      );
      throw new Error(`Failed to copy to clipboard: ${error.message}`);
    }
  }

  /**
   * Get conversion information for display
   * @param {Object} svgData - SVG data object
   * @param {Object} settings - Conversion settings
   * @returns {Object} Conversion information
   */
  static getConversionInfo(svgData, settings) {
    try {
      const normalizedSettings = normalizeSettings(settings);
      const format = normalizedSettings.format.toUpperCase();

      // For SVG format, return original dimensions and no conversion info
      if (normalizedSettings.format === "svg") {
        return {
          originalSize: `${svgData.width} × ${svgData.height}px`,
          outputSize: `${svgData.width} × ${svgData.height}px (Original)`,
          format: format,
          scale: "1x (Original)",
          isCustomSize: false,
          transparent: true,
          backgroundColor: "Original",
          isOriginalSVG: true,
        };
      }

      const dimensions = calculateOutputDimensions(svgData, normalizedSettings);

      return {
        originalSize: `${svgData.width} × ${svgData.height}px`,
        outputSize: `${dimensions.width} × ${dimensions.height}px`,
        format: format,
        scale: `${dimensions.scale.toFixed(1)}x`,
        isCustomSize: !!(
          normalizedSettings.customWidth && normalizedSettings.customHeight
        ),
        transparent: normalizedSettings.transparentBg,
        backgroundColor: normalizedSettings.transparentBg
          ? "transparent"
          : normalizedSettings.bgColor,
        isOriginalSVG: false,
      };
    } catch (error) {
      console.error(
        `${EXTENSION_CONFIG.logPrefix} Error getting conversion info:`,
        error
      );
      return {
        originalSize: "Unknown",
        outputSize: "Unknown",
        format: "PNG",
        scale: "1x",
        isCustomSize: false,
        transparent: false,
        backgroundColor: "#ffffff",
        isOriginalSVG: false,
      };
    }
  }
}
