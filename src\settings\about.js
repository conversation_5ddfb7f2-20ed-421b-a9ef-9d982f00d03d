/**
 * Enhanced About Page Functionalities - Modern & Beautiful UI
 * Handles testimonials carousel, animations, and interactive elements
 */

// Enhanced Configuration
const ABOUT_CONFIG = {
  carouselInterval: 8000, // Increased interval for better UX
  extensionVersion: "2.0.0",
  lastUpdated: "June 10, 2025",
  animationDelay: 300,
  progressAnimationDelay: 500,
  particleCount: 25, // Increased particles
  typewriterSpeed: 60, // Slightly faster typing
  scrollThreshold: 0.1, // Threshold for scroll animations
  parallaxIntensity: 0.3, // Parallax effect intensity
};

// Enhanced State Management
let isPageVisible = true;
let testimonialInterval = null;
let animationObserver = null;
let particleAnimation = null;
let parallaxElements = [];
let hoverEffects = new Map();
let counters = new Map();

// Initialize when DOM is ready
if (document.readyState === "loading") {
  document.addEventListener("DOMContentLoaded", () => {
    setTimeout(initAboutPage, 100);
  });
} else {
  setTimeout(initAboutPage, 100);
}

// Enhanced visibility handling
document.addEventListener("visibilitychange", function () {
  isPageVisible = !document.hidden;
  if (window.testimonialCarousel) {
    if (isPageVisible) {
      window.testimonialCarousel.resume();
      resumeParticles();
    } else {
      window.testimonialCarousel.pause();
      pauseParticles();
    }
  }
});

/**
 * Enhanced initialization with improved error handling and performance
 */
function initAboutPage() {
  // Initialize components
  if (performance.now() < 100) {
    // Page just loaded
    setTimeout(() => {
      initializeTestimonialCarousel();
      initCardAnimations();
      initFloatingElements();
      updateVersionInfo();
    }, 300);
  } else {
    // Components already initialized, just update
    initializeTestimonialCarousel();
    updateVersionInfo();
  }

  // Initialize features with enhanced priority and dependencies
  const features = [
    { name: "Extension Info", func: updateExtensionInfo, priority: 1 },
    { name: "Enhanced Animations", func: initEnhancedAnimations, priority: 2 },
    { name: "Scroll Animations", func: initScrollAnimations, priority: 3 },
    {
      name: "Testimonial Carousel",
      func: initTestimonialCarousel,
      priority: 4,
    },
    {
      name: "Interactive Elements",
      func: initInteractiveElements,
      priority: 5,
    },
    {
      name: "Enhanced Particles",
      func: initEnhancedParticleSystem,
      priority: 6,
    },
    { name: "Developer Image", func: preloadDeveloperImage, priority: 7 },
    { name: "Typewriter Effect", func: initTypewriterEffect, priority: 8 },
    { name: "Parallax Effects", func: initParallaxEffects, priority: 9 },
    { name: "Card Hover Effects", func: initCardHoverEffects, priority: 10 },
    { name: "Performance Monitor", func: initPerformanceMonitor, priority: 11 },
  ];

  // Execute with enhanced error handling
  features
    .sort((a, b) => a.priority - b.priority)
    .forEach(({ name, func }) => {
      try {
        func();
        console.log(`✨ SVG2PNG About: ${name} initialized successfully`);
      } catch (error) {
        console.error(`❌ SVG2PNG About: Error initializing ${name}:`, error);
        // Graceful degradation
        if (name === "Enhanced Particles" || name === "Parallax Effects") {
          console.log(
            `🔄 SVG2PNG About: Continuing without ${name} for performance`
          );
        }
      }
    });

  // Initialize global event listeners with debouncing
  initEnhancedGlobalEventListeners();

  // Set up performance monitoring
  setTimeout(() => {
    measureInitialPerformance();
  }, 1000);
}

/**
 * Enhanced testimonial carousel with advanced swap animations
 */
function initializeTestimonialCarousel() {
  const carousel = document.getElementById("testimonialCarousel");
  if (!carousel) return;

  const testimonials = carousel.querySelectorAll(".testimonial");
  const dots = carousel.querySelectorAll(".testimonial-dot");
  const prevBtn = carousel.querySelector(".prev-btn");
  const nextBtn = carousel.querySelector(".next-btn");

  if (testimonials.length === 0) return;

  // Enhanced Configuration
  const config = {
    autoplayDelay: 5000, // Longer delay for better UX
    transitionDuration: 800, // Smoother, longer transition
    pauseOnHover: true,
    enableKeyboard: true,
    enableTouch: true,
    animationEasing: "cubic-bezier(0.25, 0.46, 0.45, 0.94)",
    enableParallax: true,
    enable3D: true,
    animationVariants: ["slide", "fade", "flip", "scale"],
  };

  let currentIndex = 0;
  let autoplayInterval = null;
  let progressInterval = null;
  let isTransitioning = false;
  let isHovered = false;
  let isFocused = false;
  let isVisible = true;
  let lastDirection = "next";
  let animationVariant = "slide";
  let touchStartX = 0;
  let touchEndX = 0;

  // Enhanced initialization
  function init() {
    // Clean up all testimonials
    testimonials.forEach((testimonial, i) => {
      testimonial.classList.remove(
        "active",
        "entering",
        "exiting",
        "slide-left",
        "slide-right"
      );
      testimonial.style.transition = `all ${config.transitionDuration}ms ${config.animationEasing}`;
      testimonial.style.opacity = "0";
      testimonial.style.transform =
        "translateX(100%) rotateY(45deg) scale(0.8)";
      testimonial.style.pointerEvents = "none";
      testimonial.style.filter = "blur(3px) brightness(0.7)";
    });

    // Show first testimonial with enhanced animation
    showTestimonial(0, false);

    // Create enhanced progress bar
    createProgressBar();

    // Setup enhanced event listeners
    setupEventListeners();

    // Start autoplay with enhanced features
    startAutoplay();

    // Initialize touch handlers
    initTouchHandlers();

    // Initialize intersection observer for performance
    initIntersectionObserver();
  }

  // Enhanced testimonial display with advanced animations
  function showTestimonial(index, animate = true, direction = null) {
    if (isTransitioning) return;

    // Validate index
    index = Math.max(0, Math.min(index, testimonials.length - 1));
    if (index === currentIndex && animate) return;

    isTransitioning = true;

    // Determine animation direction
    const prevIndex = currentIndex;
    const actualDirection =
      direction || (index > currentIndex ? "next" : "prev");
    lastDirection = actualDirection;

    // Update current index
    currentIndex = index;

    // Update dots with enhanced animation
    updateDots(index);

    // Update ARIA attributes
    updateAriaAttributes(index);

    if (animate) {
      // Enhanced animated transition
      performEnhancedTransition(prevIndex, index, actualDirection);
    } else {
      // Instant change with enhanced styling
      performInstantChange(index);
    }

    // Announce to screen readers
    announceChange(index);

    // Reset transition flag after animation completes
    setTimeout(() => {
      isTransitioning = false;
    }, config.transitionDuration + 100);
  }

  // Enhanced transition with 3D effects and direction awareness
  function performEnhancedTransition(prevIndex, newIndex, direction) {
    const currentTestimonial = testimonials[newIndex];
    const prevTestimonial = testimonials[prevIndex];

    if (prevTestimonial && prevIndex !== newIndex) {
      // Enhanced exit animation
      prevTestimonial.classList.add("exiting");
      prevTestimonial.classList.remove("active");

      // Direction-based exit transforms
      if (direction === "next") {
        prevTestimonial.style.transform =
          "translateX(-100%) rotateY(-45deg) scale(0.8) translateZ(-50px)";
        prevTestimonial.classList.add("slide-left");
      } else {
        prevTestimonial.style.transform =
          "translateX(100%) rotateY(45deg) scale(0.8) translateZ(-50px)";
        prevTestimonial.classList.add("slide-right");
      }

      prevTestimonial.style.opacity = "0";
      prevTestimonial.style.filter = "blur(8px) brightness(0.5)";
      prevTestimonial.style.pointerEvents = "none";

      // Clean up after exit animation
      setTimeout(() => {
        prevTestimonial.classList.remove(
          "exiting",
          "slide-left",
          "slide-right"
        );
      }, config.transitionDuration);
    }

    // Enhanced entrance animation
    setTimeout(() => {
      currentTestimonial.classList.add("entering", "active");

      // Direction-based entrance setup
      if (direction === "next") {
        currentTestimonial.style.transform =
          "translateX(100%) rotateY(45deg) scale(0.8) translateZ(-50px)";
        currentTestimonial.classList.add("slide-from-right");
      } else {
        currentTestimonial.style.transform =
          "translateX(-100%) rotateY(-45deg) scale(0.8) translateZ(-50px)";
        currentTestimonial.classList.add("slide-from-left");
      }

      // Trigger entrance animation
      setTimeout(() => {
        currentTestimonial.style.opacity = "1";
        currentTestimonial.style.transform =
          "translateX(0) rotateY(0deg) scale(1) translateZ(0px)";
        currentTestimonial.style.filter = "blur(0px) brightness(1)";
        currentTestimonial.style.pointerEvents = "auto";
      }, 50);

      // Clean up after entrance animation
      setTimeout(() => {
        currentTestimonial.classList.remove(
          "entering",
          "slide-from-right",
          "slide-from-left"
        );
      }, config.transitionDuration);
    }, 100);
  }

  // Enhanced instant change for initialization
  function performInstantChange(index) {
    testimonials.forEach((testimonial, i) => {
      if (i === index) {
        testimonial.style.opacity = "1";
        testimonial.style.transform =
          "translateX(0) rotateY(0deg) scale(1) translateZ(0px)";
        testimonial.style.filter = "blur(0px) brightness(1)";
        testimonial.style.pointerEvents = "auto";
        testimonial.classList.add("active");
      } else {
        testimonial.style.opacity = "0";
        testimonial.style.transform =
          "translateX(100%) rotateY(45deg) scale(0.8) translateZ(-50px)";
        testimonial.style.filter = "blur(3px) brightness(0.7)";
        testimonial.style.pointerEvents = "none";
        testimonial.classList.remove("active");
      }
    });
  }

  // Enhanced dot updates with ripple effects
  function updateDots(index) {
    dots.forEach((dot, i) => {
      const isActive = i === index;
      dot.classList.toggle("active", isActive);
      dot.setAttribute("aria-selected", isActive);
      dot.setAttribute("tabindex", isActive ? "0" : "-1");

      // Add ripple effect for active dot
      if (isActive) {
        createDotRipple(dot);
      }
    });
  }

  // Create ripple effect for dots
  function createDotRipple(dot) {
    const ripple = document.createElement("div");
    ripple.style.cssText = `
      position: absolute;
      top: 50%;
      left: 50%;
      width: 0;
      height: 0;
      background: radial-gradient(circle, var(--primary-color), transparent);
      border-radius: 50%;
      transform: translate(-50%, -50%);
      pointer-events: none;
      z-index: -1;
    `;

    dot.style.position = "relative";
    dot.appendChild(ripple);

    // Animate ripple
    requestAnimationFrame(() => {
      ripple.style.width = "30px";
      ripple.style.height = "30px";
      ripple.style.opacity = "0.3";
      ripple.style.transition = "all 0.6s ease";
    });

    // Remove ripple after animation
    setTimeout(() => {
      if (ripple.parentNode) {
        ripple.parentNode.removeChild(ripple);
      }
    }, 600);
  }

  // Update ARIA attributes
  function updateAriaAttributes(index) {
    testimonials.forEach((testimonial, i) => {
      testimonial.setAttribute("aria-hidden", i !== index);
      testimonial.setAttribute("tabindex", i === index ? "0" : "-1");
    });
  }

  // Enhanced screen reader announcements
  function announceChange(index) {
    const testimonial = testimonials[index];
    const authorName = testimonial.querySelector(".profile-name")?.textContent;
    const testimonialText =
      testimonial.querySelector(".testimonial-text")?.textContent;

    if (authorName) {
      const announcement = `Testimonial ${index + 1} of ${
        testimonials.length
      } from ${authorName}${
        testimonialText ? ": " + testimonialText.substring(0, 100) + "..." : ""
      }`;

      // Create enhanced announcement element
      const announcer = document.createElement("div");
      announcer.setAttribute("aria-live", "polite");
      announcer.setAttribute("aria-atomic", "true");
      announcer.style.cssText = `
        position: absolute !important;
        left: -10000px !important;
        width: 1px !important;
        height: 1px !important;
        overflow: hidden !important;
      `;
      announcer.textContent = announcement;

      document.body.appendChild(announcer);
      setTimeout(() => document.body.removeChild(announcer), 2000);
    }
  }

  // Enhanced navigation functions
  function nextTestimonial() {
    const nextIndex = (currentIndex + 1) % testimonials.length;
    showTestimonial(nextIndex, true, "next");
  }

  function prevTestimonial() {
    const prevIndex =
      (currentIndex - 1 + testimonials.length) % testimonials.length;
    showTestimonial(prevIndex, true, "prev");
  }

  // Enhanced autoplay with smoother transitions
  function startAutoplay() {
    if (autoplayInterval || isHovered || isFocused || !isVisible) return;

    autoplayInterval = setInterval(() => {
      if (!isHovered && !isFocused && isVisible && !isTransitioning) {
        nextTestimonial();
      }
    }, config.autoplayDelay);

    startProgressBar();
  }

  function stopAutoplay() {
    if (autoplayInterval) {
      clearInterval(autoplayInterval);
      autoplayInterval = null;
    }
    stopProgressBar();
  }

  function restartAutoplay() {
    stopAutoplay();
    setTimeout(startAutoplay, 300);
  }

  // Enhanced progress bar with smooth animations
  function createProgressBar() {
    let progressBar = carousel.querySelector(
      ".carousel-progress .progress-bar"
    );
    if (!progressBar) {
      const progressContainer = document.createElement("div");
      progressContainer.className = "carousel-progress";
      progressContainer.innerHTML = '<div class="progress-bar"></div>';
      carousel.appendChild(progressContainer);
      progressBar = progressContainer.querySelector(".progress-bar");
    }
  }

  function startProgressBar() {
    if (progressInterval) return;

    const progressBar = carousel.querySelector(".progress-bar");
    if (!progressBar) return;

    let progress = 0;
    const increment = 100 / (config.autoplayDelay / 50);

    progressInterval = setInterval(() => {
      if (!isHovered && !isFocused && isVisible) {
        progress += increment;
        progressBar.style.width = `${Math.min(progress, 100)}%`;

        if (progress >= 100) {
          progress = 0;
          progressBar.style.width = "0%";
        }
      }
    }, 50);
  }

  function stopProgressBar() {
    if (progressInterval) {
      clearInterval(progressInterval);
      progressInterval = null;
    }

    const progressBar = carousel.querySelector(".progress-bar");
    if (progressBar) {
      progressBar.style.width = "0%";
    }
  }

  // Enhanced touch handlers
  function initTouchHandlers() {
    carousel.addEventListener("touchstart", handleTouchStart, {
      passive: true,
    });
    carousel.addEventListener("touchend", handleTouchEnd, { passive: true });
  }

  function handleTouchStart(e) {
    touchStartX = e.touches[0].clientX;
  }

  function handleTouchEnd(e) {
    touchEndX = e.changedTouches[0].clientX;
    handleSwipe();
  }

  function handleSwipe() {
    const swipeDistance = touchStartX - touchEndX;
    const minSwipeDistance = 50;

    if (Math.abs(swipeDistance) > minSwipeDistance) {
      if (swipeDistance > 0) {
        nextTestimonial();
      } else {
        prevTestimonial();
      }
    }
  }

  // Intersection Observer for performance
  function initIntersectionObserver() {
    if ("IntersectionObserver" in window) {
      const observer = new IntersectionObserver(
        (entries) => {
          entries.forEach((entry) => {
            isVisible = entry.isIntersecting;
            if (isVisible) {
              startAutoplay();
            } else {
              stopAutoplay();
            }
          });
        },
        { threshold: 0.5 }
      );

      observer.observe(carousel);
    }
  }

  // Enhanced event listeners setup
  function setupEventListeners() {
    // Previous/Next buttons with enhanced feedback
    if (prevBtn) {
      prevBtn.addEventListener("click", (e) => {
        e.preventDefault();
        prevTestimonial();
        createButtonRipple(prevBtn, e);
        restartAutoplay();
      });

      prevBtn.addEventListener("mouseenter", () => {
        prevBtn.style.transform = "scale(1.15) translateY(-2px)";
      });

      prevBtn.addEventListener("mouseleave", () => {
        prevBtn.style.transform = "";
      });
    }

    if (nextBtn) {
      nextBtn.addEventListener("click", (e) => {
        e.preventDefault();
        nextTestimonial();
        createButtonRipple(nextBtn, e);
        restartAutoplay();
      });

      nextBtn.addEventListener("mouseenter", () => {
        nextBtn.style.transform = "scale(1.15) translateY(-2px)";
      });

      nextBtn.addEventListener("mouseleave", () => {
        nextBtn.style.transform = "";
      });
    }

    // Enhanced dot navigation
    dots.forEach((dot, index) => {
      dot.addEventListener("click", (e) => {
        e.preventDefault();
        showTestimonial(index, true, index > currentIndex ? "next" : "prev");
        createDotRipple(dot);
        restartAutoplay();
      });

      dot.addEventListener("keydown", (e) => {
        if (e.key === "Enter" || e.key === " ") {
          e.preventDefault();
          showTestimonial(index, true, index > currentIndex ? "next" : "prev");
          createDotRipple(dot);
          restartAutoplay();
        }
      });
    });

    // Enhanced hover/focus handling
    carousel.addEventListener("mouseenter", () => {
      isHovered = true;
      stopAutoplay();
    });

    carousel.addEventListener("mouseleave", () => {
      isHovered = false;
      if (isVisible && !isFocused) {
        startAutoplay();
      }
    });

    carousel.addEventListener("focusin", () => {
      isFocused = true;
      stopAutoplay();
    });

    carousel.addEventListener("focusout", () => {
      isFocused = false;
      if (isVisible && !isHovered) {
        setTimeout(startAutoplay, 300);
      }
    });

    // Enhanced keyboard navigation
    carousel.addEventListener("keydown", (e) => {
      switch (e.key) {
        case "ArrowLeft":
          e.preventDefault();
          prevTestimonial();
          restartAutoplay();
          break;
        case "ArrowRight":
          e.preventDefault();
          nextTestimonial();
          restartAutoplay();
          break;
        case "Home":
          e.preventDefault();
          showTestimonial(0, true, "prev");
          restartAutoplay();
          break;
        case "End":
          e.preventDefault();
          showTestimonial(testimonials.length - 1, true, "next");
          restartAutoplay();
          break;
      }
    });

    // Page visibility API for better performance
    document.addEventListener("visibilitychange", () => {
      isVisible = !document.hidden;
      if (isVisible && !isHovered && !isFocused) {
        startAutoplay();
      } else {
        stopAutoplay();
      }
    });
  }

  // Ripple effect disabled for cleaner appearance
  function createButtonRipple(button, event) {
    return;
  }

  // Initialize the enhanced carousel
  init();

  // Enhanced public API
  window.testimonialCarousel = {
    next: nextTestimonial,
    prev: prevTestimonial,
    goTo: (index) => showTestimonial(index, true),
    pause: stopAutoplay,
    resume: startAutoplay,
    getCurrentIndex: () => currentIndex,
    getTotalCount: () => testimonials.length,
    setAnimationVariant: (variant) => {
      if (config.animationVariants.includes(variant)) {
        animationVariant = variant;
      }
    },
    getConfig: () => ({ ...config }),
    updateConfig: (newConfig) => {
      Object.assign(config, newConfig);
      restartAutoplay();
    },
  };
}

/**
 * Enhanced scroll animations with intersection observer
 */
function initScrollAnimations() {
  if (!window.IntersectionObserver) return;

  const observerOptions = {
    threshold: ABOUT_CONFIG.scrollThreshold,
    rootMargin: "50px 0px -50px 0px",
  };

  animationObserver = new IntersectionObserver((entries) => {
    entries.forEach((entry) => {
      if (entry.isIntersecting) {
        const element = entry.target;

        // Trigger different animations based on element type
        if (element.classList.contains("stat-number")) {
          animateEnhancedCounter(element);
        } else if (element.classList.contains("progress-fill")) {
          animateProgressBar(element);
        } else if (element.classList.contains("resource-icon")) {
          element.classList.add("visible");
          element.style.animation =
            "bounce-in 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55)";
        } else if (element.classList.contains("feature-icon")) {
          element.style.animation = `fadeInUp 0.6s ease ${
            Math.random() * 0.3
          }s both`;
        } else if (element.classList.contains("about-card")) {
          element.style.animation = `fadeInUp 0.8s cubic-bezier(0.4, 0, 0.2, 1) both`;
        }

        // Unobserve after animation
        animationObserver.unobserve(element);
      }
    });
  }, observerOptions);

  // Observe elements for animation
  const elementsToObserve = [
    ...document.querySelectorAll(".stat-number"),
    ...document.querySelectorAll(".progress-fill"),
    ...document.querySelectorAll(".resource-icon"),
    ...document.querySelectorAll(".feature-icon"),
    ...document.querySelectorAll(".about-card:not(.animated)"),
  ];

  elementsToObserve.forEach((el) => {
    if (el) animationObserver.observe(el);
  });
}

/**
 * Enhanced counter animation with easing
 */
function animateEnhancedCounter(element) {
  const target = element.textContent.replace(/[^\d]/g, "");
  const numericTarget = parseInt(target) || 0;

  if (numericTarget === 0 || counters.has(element)) return;

  counters.set(element, true);

  const duration = 2000;
  const startTime = performance.now();
  const startValue = 0;

  function updateCounter(currentTime) {
    const elapsed = currentTime - startTime;
    const progress = Math.min(elapsed / duration, 1);

    // Easing function for smooth animation
    const easeOutCubic = 1 - Math.pow(1 - progress, 3);
    const currentValue = Math.floor(
      startValue + (numericTarget - startValue) * easeOutCubic
    );

    // Update text with formatting
    if (element.textContent.includes("%")) {
      element.textContent = currentValue + "%";
    } else if (element.textContent.includes("+")) {
      element.textContent = currentValue + "+";
    } else {
      element.textContent = currentValue;
    }

    if (progress < 1) {
      requestAnimationFrame(updateCounter);
    }
  }

  requestAnimationFrame(updateCounter);
}

/**
 * Enhanced progress bar animation
 */
function animateProgressBar(element) {
  const targetWidth = element.style.width || "0%";
  element.style.width = "0%";
  element.style.transition = "width 2s cubic-bezier(0.4, 0, 0.2, 1)";

  setTimeout(() => {
    element.style.width = targetWidth;
  }, 100);
}

/**
 * Enhanced interactive elements with better feedback
 */
function initInteractiveElements() {
  initEnhancedButtonEffects();
  initEnhancedCardEffects();
  initEnhancedLinkEffects();
  initEnhancedAccessibilityFeatures();
}

/**
 * Enhanced button effects with ripple and hover states
 */
function initEnhancedButtonEffects() {
  const buttons = document.querySelectorAll(".btn-support, .dev-link");

  buttons.forEach((button) => {
    // Add ripple effect on click
    button.addEventListener("click", function (e) {
      createAdvancedRippleEffect(e, this);
    });

    // Simplified hover effects
    button.addEventListener("mouseenter", function () {
      this.style.transform = "translateY(-2px)";
      this.style.transition = "all 0.3s ease";
    });

    button.addEventListener("mouseleave", function () {
      this.style.transform = "translateY(0)";
    });

    // Enhanced focus handling
    button.addEventListener("focus", function () {
      this.style.boxShadow =
        "0 0 0 4px rgba(0, 120, 212, 0.3), 0 8px 25px rgba(0, 120, 212, 0.2)";
    });

    button.addEventListener("blur", function () {
      this.style.boxShadow = "";
    });
  });
}

/**
 * Enhanced card effects with parallax and hover
 */
function initEnhancedCardEffects() {
  const cards = document.querySelectorAll(".about-card");

  cards.forEach((card) => {
    // Enhanced hover effect with tilt
    card.addEventListener("mouseenter", function (e) {
      if (!this.classList.contains("interactive")) return;

      const rect = this.getBoundingClientRect();
      const centerX = rect.left + rect.width / 2;
      const centerY = rect.top + rect.height / 2;

      this.style.transformOrigin = `${centerX}px ${centerY}px`;
      this.style.transform =
        "translateY(-12px) rotateX(5deg) rotateY(5deg) scale(1.02)";
      this.style.transition = "all 0.4s cubic-bezier(0.4, 0, 0.2, 1)";
    });

    card.addEventListener("mouseleave", function () {
      this.style.transform =
        "translateY(0) rotateX(0deg) rotateY(0deg) scale(1)";
    });

    // Mouse move effect for subtle tilt
    card.addEventListener("mousemove", function (e) {
      if (!this.classList.contains("interactive")) return;

      const rect = this.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const y = e.clientY - rect.top;
      const centerX = rect.width / 2;
      const centerY = rect.height / 2;

      const rotateX = ((y - centerY) / centerY) * -5;
      const rotateY = ((x - centerX) / centerX) * 5;

      this.style.transform = `translateY(-12px) rotateX(${rotateX}deg) rotateY(${rotateY}deg) scale(1.02)`;
    });
  });
}

/**
 * Enhanced link effects
 */
function initEnhancedLinkEffects() {
  const links = document.querySelectorAll(".dev-link");

  links.forEach((link) => {
    // Add icon animation
    const icon = link.querySelector("svg");
    if (icon) {
      link.addEventListener("mouseenter", () => {
        icon.style.transform = "scale(1.2) rotate(12deg)";
        icon.style.transition =
          "all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55)";
      });

      link.addEventListener("mouseleave", () => {
        icon.style.transform = "scale(1) rotate(0deg)";
      });
    }
  });
}

/**
 * Enhanced accessibility features
 */
function initEnhancedAccessibilityFeatures() {
  // Enhanced keyboard navigation
  document.addEventListener("keydown", (e) => {
    if (e.key === "Tab") {
      document.body.classList.add("keyboard-navigation");
    }
  });

  document.addEventListener("mousedown", () => {
    document.body.classList.remove("keyboard-navigation");
  });

  // Enhanced ARIA labels and descriptions
  const cards = document.querySelectorAll(".about-card");
  cards.forEach((card, index) => {
    card.setAttribute("aria-describedby", `card-description-${index}`);

    const heading = card.querySelector("h3");
    if (heading) {
      heading.id = `card-heading-${index}`;
      card.setAttribute("aria-labelledby", `card-heading-${index}`);
    }
  });

  // Enhanced screen reader support
  const carousel = document.getElementById("testimonialCarousel");
  if (carousel) {
    carousel.setAttribute("aria-live", "polite");
    carousel.setAttribute("aria-label", "User testimonials carousel");
  }
}

/**
 * Ripple effect disabled for cleaner appearance
 */
function createAdvancedRippleEffect(event, element) {
  return;
}

/**
 * Ripple effect disabled for cleaner appearance
 */
function createEnhancedRippleEffect(element) {
  return;
}

/**
 * Enhanced particle system with better performance
 */
function initEnhancedParticleSystem() {
  if (!window.requestAnimationFrame || window.innerWidth < 768) return;

  const container = document.querySelector(".about-header");
  if (!container) return;

  const particles = [];
  const particleCount = Math.min(ABOUT_CONFIG.particleCount, 30);

  // Create particle container
  const particleContainer = document.createElement("div");
  particleContainer.style.cssText = `
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 0;
    overflow: hidden;
  `;
  container.style.position = "relative";
  container.appendChild(particleContainer);

  // Enhanced particle creation
  function createEnhancedParticle() {
    return {
      x: Math.random() * container.offsetWidth,
      y: Math.random() * container.offsetHeight,
      size: Math.random() * 4 + 2,
      speedX: (Math.random() - 0.5) * 0.5,
      speedY: (Math.random() - 0.5) * 0.5,
      opacity: Math.random() * 0.5 + 0.1,
      element: null,
      life: Math.random() * 2000 + 3000,
      age: 0,
    };
  }

  // Create particle elements
  for (let i = 0; i < particleCount; i++) {
    const particle = createEnhancedParticle();
    const element = document.createElement("div");

    element.style.cssText = `
      position: absolute;
      width: ${particle.size}px;
      height: ${particle.size}px;
      background: radial-gradient(circle, rgba(99, 102, 241, ${particle.opacity}) 0%, transparent 70%);
      border-radius: 50%;
      pointer-events: none;
      will-change: transform, opacity;
    `;

    particle.element = element;
    particleContainer.appendChild(element);
    particles.push(particle);
  }

  // Enhanced animation loop with performance optimization
  function animateEnhancedParticles() {
    if (!isPageVisible) {
      particleAnimation = requestAnimationFrame(animateEnhancedParticles);
      return;
    }

    particles.forEach((particle) => {
      particle.age += 16; // Approximate frame time

      // Update position
      particle.x += particle.speedX;
      particle.y += particle.speedY;

      // Wrap around edges
      if (particle.x > container.offsetWidth) particle.x = 0;
      if (particle.x < 0) particle.x = container.offsetWidth;
      if (particle.y > container.offsetHeight) particle.y = 0;
      if (particle.y < 0) particle.y = container.offsetHeight;

      // Update opacity based on life
      const lifeRatio = particle.age / particle.life;
      const opacity = particle.opacity * (1 - lifeRatio);

      // Apply transforms
      particle.element.style.transform = `translate3d(${particle.x}px, ${particle.y}px, 0)`;
      particle.element.style.opacity = Math.max(0, opacity);

      // Reset particle if life expired
      if (particle.age >= particle.life) {
        particle.age = 0;
        particle.x = Math.random() * container.offsetWidth;
        particle.y = Math.random() * container.offsetHeight;
      }
    });

    particleAnimation = requestAnimationFrame(animateEnhancedParticles);
  }

  // Start animation
  animateEnhancedParticles();
}

/**
 * Parallax effects for enhanced visual depth
 */
function initParallaxEffects() {
  if (window.innerWidth < 768) return; // Skip on mobile for performance

  const parallaxElements = document.querySelectorAll(
    ".about-card, .feature-icon, .developer-avatar"
  );

  let ticking = false;

  function updateParallax() {
    const scrollTop = window.pageYOffset;

    parallaxElements.forEach((element, index) => {
      const rect = element.getBoundingClientRect();
      const elementTop = rect.top + scrollTop;
      const elementHeight = rect.height;
      const windowHeight = window.innerHeight;

      // Calculate parallax offset
      const parallaxSpeed = ((index % 3) + 1) * ABOUT_CONFIG.parallaxIntensity;
      const yPos = -(scrollTop - elementTop) * parallaxSpeed;

      // Only apply if element is in viewport
      if (rect.top < windowHeight && rect.bottom > 0) {
        element.style.transform = `translateY(${yPos}px)`;
      }
    });

    ticking = false;
  }

  function requestParallaxUpdate() {
    if (!ticking) {
      requestAnimationFrame(updateParallax);
      ticking = true;
    }
  }

  // Throttled scroll listener
  window.addEventListener("scroll", requestParallaxUpdate, { passive: true });
}

/**
 * Enhanced card hover effects with 3D transforms
 */
function initCardHoverEffects() {
  const cards = document.querySelectorAll(".about-card");

  cards.forEach((card) => {
    let hoverEffect = null;

    card.addEventListener("mouseenter", function () {
      if (hoverEffect) clearTimeout(hoverEffect);

      this.style.transition = "all 0.4s cubic-bezier(0.4, 0, 0.2, 1)";
      this.style.transform = "translateY(-8px) rotateX(2deg) scale(1.02)";
      this.style.boxShadow =
        "0 20px 40px rgba(0, 0, 0, 0.1), 0 10px 20px rgba(0, 120, 212, 0.1)";
    });

    card.addEventListener("mouseleave", function () {
      hoverEffect = setTimeout(() => {
        this.style.transform = "translateY(0) rotateX(0deg) scale(1)";
        this.style.boxShadow = "";
      }, 50);
    });
  });
}

/**
 * Enhanced typewriter effect with cursor animation
 */
function initTypewriterEffect() {
  const typewriterElements = document.querySelectorAll("[data-typewriter]");

  typewriterElements.forEach((element) => {
    const text = element.textContent;
    const speed =
      parseInt(element.dataset.speed) || ABOUT_CONFIG.typewriterSpeed;

    element.textContent = "";
    element.style.borderRight = "2px solid var(--primary-color)";
    element.style.animation = "cursor-blink 1s infinite";

    let i = 0;
    function typeChar() {
      if (i < text.length) {
        element.textContent += text.charAt(i);
        i++;
        setTimeout(typeChar, speed + Math.random() * 20); // Add slight randomness
      } else {
        element.style.borderRight = "none";
        element.style.animation = "none";
      }
    }

    // Add cursor animation keyframes
    if (!document.querySelector("#cursor-blink-keyframes")) {
      const style = document.createElement("style");
      style.id = "cursor-blink-keyframes";
      style.textContent = `
        @keyframes cursor-blink {
          0%, 50% { border-right-color: var(--primary-color); }
          51%, 100% { border-right-color: transparent; }
        }
      `;
      document.head.appendChild(style);
    }

    setTimeout(typeChar, 1000);
  });
}

/**
 * Enhanced performance monitoring
 */
function initPerformanceMonitor() {
  if (!window.performance) return;

  let frameCount = 0;
  let lastTime = performance.now();
  let fps = 60;

  function measureFPS() {
    frameCount++;
    const currentTime = performance.now();

    if (currentTime >= lastTime + 1000) {
      fps = Math.round((frameCount * 1000) / (currentTime - lastTime));
      frameCount = 0;
      lastTime = currentTime;

      // Adjust animations based on performance
      if (fps < 30) {
        document.body.classList.add("reduced-motion");
        console.log(
          "⚡ SVG2PNG About: Reduced animations for better performance"
        );
      } else if (fps > 50) {
        document.body.classList.remove("reduced-motion");
      }
    }

    requestAnimationFrame(measureFPS);
  }

  measureFPS();
}

/**
 * Enhanced global event listeners with debouncing
 */
function initEnhancedGlobalEventListeners() {
  let resizeTimeout;

  // Debounced resize handler
  window.addEventListener(
    "resize",
    () => {
      clearTimeout(resizeTimeout);
      resizeTimeout = setTimeout(() => {
        // Recalculate particle positions
        if (particles && particles.length > 0) {
          particles.forEach((particle) => {
            particle.x = Math.random() * window.innerWidth;
            particle.y = Math.random() * window.innerHeight;
          });
        }

        // Restart testimonial carousel with new dimensions
        if (window.testimonialCarousel) {
          window.testimonialCarousel.pause();
          setTimeout(() => window.testimonialCarousel.resume(), 300);
        }
      }, 250);
    },
    { passive: true }
  );

  // Enhanced keyboard shortcuts
  document.addEventListener("keydown", (e) => {
    if (e.target.matches("input, textarea, select")) return;

    switch (e.key) {
      case "ArrowLeft":
        if (window.testimonialCarousel) {
          e.preventDefault();
          window.testimonialCarousel.prevTestimonial();
        }
        break;
      case "ArrowRight":
        if (window.testimonialCarousel) {
          e.preventDefault();
          window.testimonialCarousel.nextTestimonial();
        }
        break;
      case "Escape":
        // Close any modals or focused elements
        document.activeElement?.blur();
        break;
    }
  });

  // Enhanced intersection observer for lazy loading
  if (window.IntersectionObserver) {
    const lazyLoadObserver = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const img = entry.target;
            if (img.dataset.src) {
              img.src = img.dataset.src;
              img.removeAttribute("data-src");
              lazyLoadObserver.unobserve(img);
            }
          }
        });
      },
      { rootMargin: "50px" }
    );

    document.querySelectorAll("img[data-src]").forEach((img) => {
      lazyLoadObserver.observe(img);
    });
  }
}

/**
 * Measure initial performance metrics
 */
function measureInitialPerformance() {
  if (!window.performance) return;

  const perfData = performance.getEntriesByType("navigation")[0];
  if (perfData) {
    const loadTime = perfData.loadEventEnd - perfData.loadEventStart;
    const domContentLoadedTime =
      perfData.domContentLoadedEventEnd - perfData.domContentLoadedEventStart;

    console.log(
      `🚀 SVG2PNG About: Page loaded in ${loadTime}ms, DOM ready in ${domContentLoadedTime}ms`
    );

    // Show performance badge (optional)
    if (loadTime < 1000) {
      const perfBadge = document.createElement("div");
      perfBadge.textContent = "⚡ Fast Loading";
      perfBadge.style.cssText = `
        position: fixed;
        bottom: 20px;
        right: 20px;
        background: var(--primary-color);
        color: white;
        padding: 8px 12px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
        z-index: 1000;
        animation: slideInUp 0.5s ease, fadeOut 0.5s ease 2s both;
      `;
      document.body.appendChild(perfBadge);
      setTimeout(() => perfBadge.remove(), 3000);
    }
  }
}

/**
 * Utility functions for particle control
 */
function pauseParticles() {
  if (particleAnimation) {
    cancelAnimationFrame(particleAnimation);
    particleAnimation = null;
  }
}

function resumeParticles() {
  if (!particleAnimation && particles && particles.length > 0) {
    animateEnhancedParticles();
  }
}

/**
 * Enhanced extension info update
 */
function updateExtensionInfo() {
  const updateElements = (selector, text, animate = false) => {
    const elements = document.querySelectorAll(selector);
    elements.forEach((element) => {
      if (element) {
        if (animate) {
          element.style.opacity = "0";
          element.style.transition = "opacity 0.3s ease";
          setTimeout(() => {
            element.textContent = text;
            element.style.opacity = "1";
          }, 150);
        } else {
          element.textContent = text;
        }
      }
    });
  };

  // Update version information with animation
  updateElements(
    "#extensionVersion, #extensionVersion2",
    ABOUT_CONFIG.extensionVersion,
    true
  );
  updateElements("#lastUpdated", ABOUT_CONFIG.lastUpdated, true);

  // Add version change indicator
  const versionElements = document.querySelectorAll(
    "#extensionVersion, #extensionVersion2"
  );
  versionElements.forEach((element) => {
    const indicator = document.createElement("span");
    indicator.textContent = " ✨";
    indicator.style.cssText = `
      opacity: 0;
      animation: fadeIn 0.5s ease 1s both, pulse 1s ease 1.5s infinite;
      color: var(--primary-color);
    `;
    element.parentNode.appendChild(indicator);
    setTimeout(() => indicator.remove(), 5000);
  });
}

/**
 * Enhanced developer image preloading with better error handling
 */
function preloadDeveloperImage() {
  const avatarContainer = document.querySelector(".developer-avatar");
  const img = document.querySelector(".developer-photo");

  if (!img || !avatarContainer) return;

  avatarContainer.classList.add("loading");

  const preloadImg = new Image();
  const placeholder = document.createElement("div");

  // Enhanced placeholder
  placeholder.className = "avatar-placeholder";
  placeholder.style.cssText = `
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(0, 120, 212, 0.1), rgba(75, 150, 215, 0.2));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: var(--primary-color);
    z-index: 1;
  `;
  placeholder.innerHTML = "👨‍💻";

  preloadImg.onload = () => {
    img.style.opacity = "0";
    setTimeout(() => {
      img.src = preloadImg.src;
      img.style.transition = "opacity 0.5s ease";
      img.style.opacity = "1";
      avatarContainer.classList.remove("loading");
      placeholder.remove();
    }, 200);
  };

  preloadImg.onerror = () => {
    avatarContainer.classList.remove("loading");
    avatarContainer.classList.add("error");
    placeholder.innerHTML = "❌";
    placeholder.style.background = "rgba(255, 99, 99, 0.1)";
    console.warn("SVG2PNG About: Failed to load developer image");
  };

  avatarContainer.appendChild(placeholder);
  preloadImg.src = img.src;
}

/**
 * Enhanced cleanup function
 */
function cleanup() {
  // Clear intervals and animations
  if (testimonialInterval) clearInterval(testimonialInterval);
  if (particleAnimation) cancelAnimationFrame(particleAnimation);
  if (animationObserver) animationObserver.disconnect();

  // Clear maps
  hoverEffects.clear();
  counters.clear();

  // Remove event listeners
  window.removeEventListener("scroll", requestParallaxUpdate);

  console.log("🧹 SVG2PNG About: Cleanup completed");
}

// Enhanced page unload cleanup
window.addEventListener("beforeunload", cleanup);

// Enhanced error boundary
window.addEventListener("error", (e) => {
  console.error("SVG2PNG About: Unexpected error:", e.error);
  // Graceful degradation for critical errors
  if (e.error && e.error.message.includes("animation")) {
    document.body.classList.add("reduced-motion");
  }
});

// Export for debugging
if (typeof window !== "undefined") {
  window.SVG2PNGAbout = {
    config: ABOUT_CONFIG,
    carousel: () => window.testimonialCarousel,
    cleanup,
    version: "2.0.0",
  };
}

// Enhanced Version Card Functionality
document.addEventListener("DOMContentLoaded", function () {
  initializeVersionCard();
});

function initializeVersionCard() {
  // Add interactive functionality to version card buttons
  const checkUpdateBtn = document.getElementById("checkUpdateBtn");
  const viewChangelogBtn = document.getElementById("viewChangelogBtn");

  if (checkUpdateBtn) {
    checkUpdateBtn.addEventListener("click", handleCheckUpdates);
  }

  if (viewChangelogBtn) {
    viewChangelogBtn.addEventListener("click", handleViewChangelog);
  }

  // Animate progress bar on load
  animateProgressBar();

  // Add hover effects to timeline items
  addTimelineInteractions();

  // Initialize version stats with animation
  animateVersionStats();
}

function handleCheckUpdates() {
  const button = document.getElementById("checkUpdateBtn");
  const originalText = button.innerHTML;

  // Show loading state
  button.innerHTML = `
        <svg class="animate-spin" width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4" stroke-dasharray="31.416" stroke-dashoffset="31.416">
                <animate attributeName="stroke-dasharray" dur="2s" values="0 31.416;15.708 15.708;0 31.416" repeatCount="indefinite"/>
                <animate attributeName="stroke-dashoffset" dur="2s" values="0;-15.708;-31.416" repeatCount="indefinite"/>
            </circle>
        </svg>
        Checking...
    `;
  button.disabled = true;

  // Simulate update check
  setTimeout(() => {
    button.innerHTML = `
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" width="18" height="18">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
            </svg>
            Up to Date
        `;
    button.style.background =
      "linear-gradient(135deg, #10b981 0%, #059669 100%)";

    setTimeout(() => {
      button.innerHTML = originalText;
      button.disabled = false;
      button.style.background = "";
    }, 2000);
  }, 1500);
}

function handleViewChangelog() {
  // Create and show changelog modal
  const modal = createChangelogModal();
  document.body.appendChild(modal);

  // Animate modal appearance
  setTimeout(() => {
    modal.classList.add("show");
  }, 10);

  // Add close functionality
  const closeBtn = modal.querySelector(".modal-close");
  const overlay = modal.querySelector(".modal-overlay");

  [closeBtn, overlay].forEach((element) => {
    if (element) {
      element.addEventListener("click", () => {
        modal.classList.remove("show");
        setTimeout(() => {
          document.body.removeChild(modal);
        }, 300);
      });
    }
  });
}

function createChangelogModal() {
  const modal = document.createElement("div");
  modal.className = "changelog-modal";
  modal.innerHTML = `
        <div class="modal-overlay"></div>
        <div class="modal-content">
            <div class="modal-header">
                <h3>
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" width="24" height="24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    Complete Changelog
                </h3>
                <button class="modal-close">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" width="24" height="24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            <div class="modal-body">
                <div class="changelog-content">
                    <div class="changelog-version">
                        <div class="version-header">
                            <span class="version-number">v2.0.0</span>
                            <span class="version-date">June 19, 2025</span>
                            <span class="version-badge stable">Stable Release</span>
                        </div>
                        <div class="version-changes">
                            <h4>🎉 Major Features</h4>
                            <ul>
                                <li>Multi-format export: Convert SVG to PNG, JPEG, WebP, or keep as original SVG</li>
                                <li>Batch processing with ZIP downloads: Process multiple SVGs and download as compressed archive</li>
                                <li>Clipboard integration: Copy converted images directly to clipboard for quick sharing</li>
                                <li>Advanced quality scaling: Choose from 1x to 4x resolution multipliers for crisp output</li>
                                <li>Floating toolbar: Draggable, customizable toolbar with smart positioning and auto-hide options</li>
                            </ul>
                            <h4>🚀 Enhancements</h4>
                            <ul>
                                <li>Full accessibility support: ARIA labels, keyboard navigation, and screen reader compatibility</li>
                                <li>Performance monitoring: Built-in performance tracking and memory usage optimization</li>
                                <li>Settings persistence: Save and load custom conversion presets for workflow efficiency</li>
                                <li>Background options: Toggle transparent backgrounds or set custom background colors</li>
                                <li>Smart error handling: Comprehensive error recovery with user-friendly feedback messages</li>
                            </ul>
                            <h4>🛠️ Technical Improvements</h4>
                            <ul>
                                <li>Modular architecture with service-oriented design</li>
                                <li>Enhanced memory management with automatic cleanup</li>
                                <li>Optimized canvas rendering for better performance</li>
                                <li>Context invalidation handling for extension stability</li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="changelog-version">
                        <div class="version-header">
                            <span class="version-number">v1.5.2</span>
                            <span class="version-date">June 18, 2025</span>
                            <span class="version-badge patch">Patch Release</span>
                        </div>
                        <div class="version-changes">
                            <h4>🐛 Bug Fixes</h4>
                            <ul>
                                <li>Fixed feature suggestion and bug report links in settings page</li>
                                <li>Updated developer branding and user count displays</li>
                                <li>Improved SVG icon rendering in settings interface</li>
                                <li>Corrected extension name references throughout UI</li>
                            </ul>
                            <h4>🔧 Minor Improvements</h4>
                            <ul>
                                <li>Enhanced community statistics display</li>
                                <li>Updated social media links and contact information</li>
                                <li>Improved visual aesthetics of settings cards</li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="changelog-version">
                        <div class="version-header">
                            <span class="version-number">v1.5.0</span>
                            <span class="version-date">June 15, 2025</span>
                            <span class="version-badge feature">Feature Release</span>
                        </div>
                        <div class="version-changes">
                            <h4>✨ New Features</h4>
                            <ul>
                                <li>Preset management: Save, load, and delete custom conversion settings</li>
                                <li>Progress tracking: Real-time progress bars for batch conversions</li>
                                <li>Preview functionality: Live preview of conversion results before download</li>
                                <li>Toolbar customization: Remember toolbar position and behavior preferences</li>
                            </ul>
                            <h4>🚀 Improvements</h4>
                            <ul>
                                <li>Enhanced file naming with automatic index handling for duplicates</li>
                                <li>Improved SVG parsing for complex graphics and embedded styles</li>
                                <li>Better validation for user inputs and file constraints</li>
                                <li>Optimized conversion pipeline for faster processing</li>
                            </ul>
                            <h4>🔧 Technical</h4>
                            <ul>
                                <li>Added lazy loading for improved startup performance</li>
                                <li>Implemented robust state management system</li>
                                <li>Enhanced error logging and debugging capabilities</li>
                                <li>Updated dependencies for security and compatibility</li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="changelog-version">
                        <div class="version-header">
                            <span class="version-number">v1.0.0</span>
                            <span class="version-date">May 30, 2025</span>
                            <span class="version-badge stable">Initial Stable Release</span>
                        </div>
                        <div class="version-changes">
                            <h4>🎉 Core Features</h4>
                            <ul>
                                <li>SVG to image conversion: High-quality conversion with canvas-based rendering</li>
                                <li>Multiple export formats: Support for PNG, JPEG, and WebP output</li>
                                <li>Quality settings: Adjustable resolution scaling for different use cases</li>
                                <li>Simple user interface: Clean, intuitive popup design</li>
                                <li>File downloads: Direct browser download integration</li>
                            </ul>
                            <h4>🛠️ Architecture</h4>
                            <ul>
                                <li>Content script injection for SVG detection and selection</li>
                                <li>Background service worker for download management</li>
                                <li>Modular utility system for SVG processing and validation</li>
                                <li>CSS-in-JS styling system for consistent theming</li>
                            </ul>
                            <h4>🔒 Permissions</h4>
                            <ul>
                                <li>Active tab access for SVG detection</li>
                                <li>Downloads permission for file saving</li>
                                <li>Storage access for settings persistence</li>
                                <li>Clipboard write access for copy functionality</li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="changelog-version">
                        <div class="version-header">
                            <span class="version-number">v0.9.5</span>
                            <span class="version-date">May 25, 2025</span>
                            <span class="version-badge rc">Release Candidate</span>
                        </div>
                        <div class="version-changes">
                            <h4>🐛 Bug Fixes</h4>
                            <ul>
                                <li>Resolved conversion issues with complex SVG files containing gradients and filters</li>
                                <li>Fixed memory leaks during batch processing of large file sets</li>
                                <li>Corrected progress bar accuracy calculations</li>
                                <li>Improved handling of SVG files with embedded fonts</li>
                            </ul>
                            <h4>🔧 Technical Improvements</h4>
                            <ul>
                                <li>Updated core dependencies to latest stable versions</li>
                                <li>Enhanced code documentation with JSDoc annotations</li>
                                <li>Improved error logging with detailed stack traces</li>
                                <li>Added performance benchmarking utilities</li>
                            </ul>
                            <h4>🚀 Performance</h4>
                            <ul>
                                <li>Optimized canvas rendering for faster conversion times</li>
                                <li>Reduced memory footprint during large batch operations</li>
                                <li>Improved garbage collection handling</li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="changelog-version">
                        <div class="version-header">
                            <span class="version-number">v0.9.0</span>
                            <span class="version-date">May 15, 2025</span>
                            <span class="version-badge alpha">Alpha Release</span>
                        </div>
                        <div class="version-changes">
                            <h4>✨ Initial Implementation</h4>
                            <ul>
                                <li>Basic SVG to PNG conversion functionality</li>
                                <li>Simple file selection and download mechanism</li>
                                <li>Essential UI components and user interaction</li>
                                <li>Core extension manifest and permissions setup</li>
                            </ul>
                            <h4>🛠️ Foundation</h4>
                            <ul>
                                <li>Established modular code architecture</li>
                                <li>Basic error handling and validation</li>
                                <li>Simple settings and configuration system</li>
                                <li>Initial content script and popup integration</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

  return modal;
}

function animateProgressBar() {
  const progressBar = document.querySelector(".enhanced-progress-fill");
  if (progressBar) {
    const targetWidth = progressBar.style.width;
    progressBar.style.width = "0%";

    setTimeout(() => {
      progressBar.style.width = targetWidth;
    }, 500);
  }
}

function addTimelineInteractions() {
  const timelineItems = document.querySelectorAll(".timeline-item");

  timelineItems.forEach((item, index) => {
    // Add staggered animation delay
    item.style.animationDelay = `${index * 0.2}s`;

    // Add click interaction for more details
    item.addEventListener("click", () => {
      item.classList.toggle("expanded");
    });
  });
}

function animateVersionStats() {
  const stats = document.querySelectorAll(".version-stat");

  stats.forEach((stat, index) => {
    stat.style.opacity = "0";
    stat.style.transform = "translateY(20px)";

    setTimeout(() => {
      stat.style.transition = "all 0.6s cubic-bezier(0.4, 0, 0.2, 1)";
      stat.style.opacity = "1";
      stat.style.transform = "translateY(0)";
    }, index * 150);
  });
}

// Add modal styles dynamically
const modalStyles = `
<style>
.changelog-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.changelog-modal.show {
    opacity: 1;
    visibility: visible;
}

.modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(5px);
}

.modal-content {
    position: relative;
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.98) 0%, rgba(248, 250, 252, 0.95) 100%);
    border-radius: var(--border-radius-xl);
    max-width: 800px;
    width: 90%;
    max-height: 80vh;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    border: 1px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(20px);
    transform: scale(0.9) translateY(20px);
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.changelog-modal.show .modal-content {
    transform: scale(1) translateY(0);
}

.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-xl) var(--spacing-xl) var(--spacing-lg);
    border-bottom: 1px solid rgba(226, 232, 240, 0.6);
}

.modal-header h3 {
    margin: 0;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    color: var(--text-primary);
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-bold);
}

.modal-close {
    padding: var(--spacing-sm);
    border: none;
    background: rgba(226, 232, 240, 0.3);
    border-radius: var(--border-radius-md);
    cursor: pointer;
    transition: all 0.2s ease;
    color: var(--text-secondary);
}

.modal-close:hover {
    background: rgba(239, 68, 68, 0.1);
    color: #ef4444;
    transform: scale(1.1);
}

.modal-body {
    padding: var(--spacing-lg) var(--spacing-xl);
    max-height: calc(80vh - 120px);
    overflow-y: auto;
}

.changelog-version {
    margin-bottom: var(--spacing-xl);
    border: 1px solid rgba(226, 232, 240, 0.6);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    background: rgba(255, 255, 255, 0.8);
}

.version-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-lg);
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-bottom: 1px solid rgba(226, 232, 240, 0.6);
}

.version-number {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-bold);
    color: var(--primary-color);
}

.version-date {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.version-badge {
    padding: 4px 8px;
    border-radius: var(--border-radius-full);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-semibold);
    text-transform: uppercase;
}

.version-badge.stable {
    background: rgba(16, 185, 129, 0.1);
    color: #059669;
    border: 1px solid rgba(16, 185, 129, 0.3);
}

.version-badge.beta {
    background: rgba(245, 158, 11, 0.1);
    color: #d97706;
    border: 1px solid rgba(245, 158, 11, 0.3);
}

.version-badge.alpha {
    background: rgba(239, 68, 68, 0.1);
    color: #dc2626;
    border: 1px solid rgba(239, 68, 68, 0.3);
}

.version-changes {
    padding: var(--spacing-lg);
}

.version-changes h4 {
    margin: 0 0 var(--spacing-md) 0;
    font-size: var(--font-size-md);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
}

.version-changes ul {
    margin: 0 0 var(--spacing-lg) 0;
    padding-left: var(--spacing-lg);
}

.version-changes li {
    margin-bottom: var(--spacing-xs);
    color: var(--text-secondary);
    line-height: 1.5;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

.animate-spin {
    animation: spin 1s linear infinite;
}

/* Dark theme support for modal */
[data-theme="dark"] .modal-content {
    background: linear-gradient(145deg, rgba(30, 41, 59, 0.95) 0%, rgba(51, 65, 85, 0.9) 100%);
    border-color: rgba(51, 65, 85, 0.6);
}

[data-theme="dark"] .changelog-version {
    background: rgba(51, 65, 85, 0.6);
    border-color: rgba(71, 85, 105, 0.6);
}

[data-theme="dark"] .version-header {
    background: linear-gradient(135deg, rgba(51, 65, 85, 0.8) 0%, rgba(71, 85, 105, 0.6) 100%);
    border-color: rgba(71, 85, 105, 0.6);
}
</style>
`;

// Inject styles
document.head.insertAdjacentHTML("beforeend", modalStyles);

// Removed toggleUpdateHistory function - simplified design no longer needs history toggle
